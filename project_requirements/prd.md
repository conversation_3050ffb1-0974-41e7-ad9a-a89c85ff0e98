# 0. 项目总览
本项目将采用前后端架构，实现一个`AI Agent`驱动的互动学习平台，本文档主要描述后端部分功能与开发要求。前端部分说明仅作为背景知识，供后端开发时参考。

# 1. 前后端功能划分
- 前端负责实现用户的交互操作，接收用户输入并将结果以结构化方式发送给后端的Python服务器（由FastAPI实现）；
- 后端负责实现各类 AI Agents 包括以下几个任务：
  - 设计实现不同功能的 Agents / Agents Team
  - 协调 Agents 之间的工作
  - 协调 Agents 与前端之间交互工作

# 2. 后端 Agents 的架构和任务设计
后端主要由三个 **`Agent Team`** 所组成： 
- **`Planning Team`**: 负责收集整理用户输入内容，生成具体的课程大纲、课程规划任务
- **`Teaching Team`**: 负责根据生成的课程内容，实施教学活动，回答学生的问题，提供讨论和答疑支持
- **`Monitor Team`**: 负责监测教学内容的**规划**与**执行**效果，用来形成对学生的跟踪评价，和对课程设计的长期优化建议

## 2.1 **`Planning Team`** 

### 2.1.1 角色划分与任务概述
`Planning Team` 包括如下几个 Agents：
- **`AgentTaskRouter`**: 收集用户的需求，以及用户背景信息，分析整合，生成任务并分派任务给不同的Agent
- **`AgentOutlineDesigner`**: 接收`TaskRouter`的任务指令，根据要求来生成课程大纲
- **`AgentContentGenerator`**: 接收`TaskRouter`的任务指令和`OutlineDesigner`生成对大纲，根据其要求（课程主题、用户期望学习时间、学习目标等）设计每个章节的主题，并对主题逐个进行规划，生成具体课程的全部内容
- **`AgentStandardVerifier`**: 负责审阅 `OutlineDesigner` 与 `ContentGenerator` 生成的内容，是否与相关课标对齐，检查合理性、正确性，并进行打分，给出修改意见
- **`AgentTeacher`**: 负责执行 `ContentGenerator` 生成的课程，采用对话形式，逐步推进课程内容，并响应学习者（人类用户）的反馈

### 2.1.2 各Agent基本任务说明
本节主要界定 Teaching Team 中各个Agent的功能职责：

#### **[A] AgentTaskRouter**

#### **[B] CoursePlanner**

**主要职责**：
- 接收和分析用户提供的学习主题
- 与用户交流，收集用户基本认知背景
- 根据学习者情况，评估主题的学习难度和预计所需课时
- 设计完整的课程大纲结构
- 将课程内容分解为合理的章节和子主题
- 为每个章节设定明确的学习目标
- 为每个章节确定与不同课标体系的对齐列表

**输入与输出**：
- **输入**：
  1. 用户的学习主题(用户输入)
  2. 学习目标（用户选择）
  3. 预期学习时长（用户选择）
  4. 学习者背景知识水平（用户选择）
- **输出**：
  1. 结构化的课程大纲：包含章节划分、每章核心内容、学习目标和时长安排、与课标对齐情况列表
  2. 工作状态参数（第几轮生成，得分等信息）

**工作流程**：
1. 解析用户输入的主题，确定课程主题范围
2. 生成学情调研分析问题，等待用户选择，评估用户学情和学习偏好
3. 进行学科知识图谱分析，识别关键知识点
4. 按照学习逻辑和难度递进原则组织内容
5. 设计每章节的学习目标和内容概要
6. 运行自评估（并根据响应标准修改迭代一次）
7. 提交内容给 ContentVerifier 进行审核
8. 接收 ContentVerifier 反馈进行修改迭代（5～6两个步骤交替，根据收敛条件推出迭代）

**自评估标准**：
- 课程内容的完整性和系统性
- 学习路径的合理性
- 与用户需求和学情的匹配度
- 知识点覆盖的广度和深度

#### **[C] ContentDesigner**

**主要职责**：
- 根据CoursePlanner提供的课程大纲，逐个章节设计课程内容
- 为每个章节创建详细的多媒体教学材料
- 设计教学活动、练习和案例
- 确保内容的专业性、准确性和可实施性
- 内容结构，使其易于理解和吸收

**输入与输出**：
- **输入**：
  1. CoursePlanner 生成的课程大纲、章节学习目标、课标对齐信息
  2. 目标受众信息（继承自 CoursePlanner 的输入）
- **输出**：
  1. 完整的课程内容，包括情景故事、讲解文本、图片提示词（发给图像生成服务API/MCP）、演示代码、示例、练习题、互动环节设计
  2. 工作状态参数（第几轮生成，得分等信息）

**工作流程**：
1. 分析课程大纲和学习目标要求
2. 收集和整理相关领域的专业知识
3. 按照互动教学逻辑和游戏化学习理念，设计教学流程
4. 创建配套的练习
5. 运行自评估（并根据响应标准修改迭代一次）
6. 提交内容给 ContentVerifier 进行审核
7. 接收 ContentVerifier 反馈进行修改迭代（5～6两个步骤交替，根据收敛条件推出迭代）

**自评估标准**：
- 内容的准确性和时效性
- 内容的互动特性、趣味性
- 表达的清晰度和教学可操作性
- 与学习目标的一致性
- 内容是否能够与预定课标对齐
- 示例和练习的有效性（是否能够反映教学内容， 是否能评估学习者的掌握度）

#### **[D] AgentStandardVerifier**

**主要职责**：
1. 针对 CoursePlanner 的职责：
   - 检查课程大纲与用户输入主题的一致性
   - 检查课程大纲与用户学情的匹配度
   - 检查课程大纲与课标对齐的合理性
   - 为课程大纲进行多维度评分
   - 提供课程大纲的修正意见
   - 确保内容无偏见、无错误、无侵权
2. 针对 ContentDesigner 的职责：
   - 检查课程内容与课程大纲对应章节的一致性
   - 审查课程内容的准确性和质量
   - 评估内容与用户学情的匹配度
   - 检查课程内容与对应课标的对齐情况
   - 评估课程内容材料（故事、图片、练习、互动）的合理性
   - 为课程大纲进行多维度评分
   - 提供课程大纲的修正意见

**输入与输出**：
- **输入**：
  1. CoursePlanner 生成的课程大纲
  2. ContentDesigner 生成的课程内容
- **输出**：
  1. 针对课程大纲的多维度评分+修改建议
  2. 针对课程内容的多维度评分+修改建议
  3. 工作状态参数 

**工作流程**：
1. 等待接收 CoursePlanner 生成内容
2. 运行上述主要职责中对于 CoursePlanner 生成内容的检查项
3. 生成多维评分，并给出具体的修改建议
4. 等待接收 ContentDesigner 生成内容
5. 运行上述主要职责中对于 ContentDesigner 生成内容的检查项
6. 生成多维评分，并给出具体的修改建议 
7. 重复步骤 1～6 直到触发终止条件
   - 终止条件：修改轮次 >= 3 || 多维评分 >= 合格阈值


#### **[E] AgentTeacher**

**主要职责**：
- 逐步执行 ContentDesigner 生成的课程内容
- 以对话形式与学习者展开互动学习（苏格拉底式对话风格）
- 根据学习者反馈调整对话细节和方式
- 在不同的节点位置设计问题
- 根据学生回答来评估学习者的理解程度
- 解答学习过程中学生提出的问题
- 提供游戏化体验，提供即时反馈

**输入与输出**：
- **输入**：经验证的课程内容、学习者实时反馈、学习进度信息
- **输出**：教学对话、解释说明、答疑内容、学习反馈

**工作流程**：
1. 按照课程内容开展教学对话
2. 监测学习者的理解和参与度
3. 根据学习者反应灵活调整教学方式
4. 提供个性化的解释和例子
5. 与 Monitor Team 协作，记录学习者表现和问题点

**评估标准**：
- 教学互动的流畅性和自然度
- 回应学习者问题的准确性和及时性
- 教学语言的亲和力和鼓励性
- 解释的清晰度和针对性
- 学习者的参与度和满意度

#### [Learning Team]
## 2.2 **Learning Team**

### 2.2.1 角色划分与任务概述
该 Team 包括如下几个 Agents：
- **LearningCompanion**: 作为学习者的学习伙伴，参与课堂讨论，配合 Teacher 启发人类学习者思考
- **CodeCompanion**: [暂不启用] 作为编程学习内容的专业辅助，提供代码相关支持和指导

### 2.2.2 各Agent基本任务说明
本节主要界定 Learning Team 中各个Agent的功能职责：

#### **[A] LearningCompanion**

**主要职责**：
- 作为课堂讨论的积极参与者，配合 Teacher Agent 开展教学活动
- 在 Teacher 提出问题或启发讨论时，如果人类用户没有参与回答，则加入讨论，提供一些见解，不能直接给出答案或明显的线索
- 与人类学习者建立同伴关系，降低学习心理障碍，引导人类学习者主动输出思路（费曼学习法）
- 在必要时提供补充解释和不同视角的理解
- 展示学习过程中可能遇到的困惑和解决思路

**输入与输出**：
- **输入**：
  1. Teacher Agent 的教学内容和提问
  2. 人类学习者的回应和问题
- **输出**：
  1. 对 Teacher 提问的回应和见解
  2. 补充性问题和思考方向
  3. 与人类学习者互动的讨论内容
  4. 工作状态参数

**工作流程**：
1. 监听课堂讨论，识别 Teacher 的教学意图和提问
2. 在适当时机（如人类学习者长时间不回复）加入讨论，提供见解或提出问题
3. 观察人类学习者的参与度，向人类求助，鼓励其输出
4. 与 Teacher 配合，形成多方讨论氛围
5. 在人类学习者表达困惑时提供支持性思考

**自评估标准**：
- 讨论参与的及时性和相关性
- 对促进人类学习者思考的有效程度
- 与 Teacher 互动的协调性
- 讨论内容的深度和启发性

#### **[B] CodeCompanion** [暂不启用]

**主要职责**：
- 辅助编程相关学习内容的理解和应用
- 提供代码示例和编程思路解析
- 帮助调试代码和解决编程问题
- 推荐编程最佳实践和设计模式

**输入与输出**：
- **输入**：
  1. 编程学习内容和要求
  2. 学习者的代码和编程问题
- **输出**：
  1. 代码分析和改进建议
  2. 编程思路和解决方案
  3. 工作状态参数

**注**：该 Agent 当前处于未启用状态，将在未来版本中实现。


## 2.3 **Monitor Team**

### 2.3.1 角色划分与任务概述
该 Team 包括如下几个 Agents：
- **SessionAnalyst**: 负责实时监测和分析单次学习会话中的交互数据，评估学习者在当前会话中的表现与进度
- **LearningProfiler**: 负责维护学习者的长期学习档案，分析学习模式，生成综合评估报告，并提出个性化学习建议

### 2.3.2 各Agent基本任务说明
本节主要界定 Monitor Team 中各个Agent的功能职责：

#### **[A] SessionAnalyst**

**主要职责**：
- 实时监测与记录学习会话中的交互数据
- 分析学习者在当前会话中的问题回答准确率、参与度及反应速度
- 识别学习者在特定知识点上的困惑或误解
- 评估学习者对当前学习内容的理解深度
- 将会话数据结构化并提供给 LearningProfiler 作为长期分析依据
- 向 Teacher Agent 提供实时反馈，帮助其调整教学策略

**输入与输出**：
- **输入**：
  1. Teacher 与学习者的实时对话内容
  2. 预设的学习目标和知识点
  3. 学习者的提问、回答及反应时间
  4. LearningCompanion 的互动记录
- **输出**：
  1. 当前会话的结构化数据记录
  2. 学习者参与度、理解度的实时评估指标
  3. 知识点掌握情况的映射表
  4. 对 Teacher 的实时教学建议
  5. 工作状态参数

**工作流程**：
1. 持续监听课堂对话，记录关键互动节点
2. 分析学习者回答的准确性、相关性和思维深度
3. 标记并分析学习者表现出困难的知识点
4. 生成当前会话的实时评估指标
5. 向 Teacher 提供适时的教学调整建议
6. 在会话结束时整理完整的会话报告
7. 将结构化数据传递给 LearningProfiler 进行长期分析

**自评估标准**：
- 数据收集的完整性和准确性
- 实时分析的及时性和洞察力
- 对困难点识别的精确度
- 教学建议的可操作性和有效性
- 与 Teacher Agent 的协作流畅度

#### **[B] LearningProfiler**

**主要职责**：
- 维护学习者的长期学习档案与知识图谱
- 整合多次学习会话的数据，识别学习模式与趋势
- 追踪学习者知识点掌握的进展情况
- 生成定期的学习进度报告和能力评估
- 基于历史数据提供个性化学习路径建议
- 分析教学内容与学习效果的关联，提供课程优化建议

**输入与输出**：
- **输入**：
  1. SessionAnalyst 提供的结构化会话数据
  2. 历史学习档案数据
  3. 课程大纲与学习目标
- **输出**：
  1. 更新的学习者知识图谱
  2. 长期学习进度报告
  3. 学习者能力模型评估
  4. 个性化学习建议
  5. 课程内容优化建议
  6. 工作状态参数

**工作流程**：
1. 接收并整合 SessionAnalyst 提供的会话数据
2. 更新学习者的知识图谱与能力模型
3. 分析学习者在各知识点上的长期进步趋势
4. 识别学习者的优势领域和需要加强的弱点
5. 生成全面的学习档案报告
6. 提供后续学习建议和个性化资源推荐
7. 向 CoursePlanner 和 ContentDesigner 提供课程优化反馈

**自评估标准**：
- 长期数据分析的全面性和准确性
- 学习模式识别的精确度
- 进度报告的清晰度和实用性
- 个性化建议的针对性和可行性
- 课程优化建议的有效性

### 2.3.3 Monitor Team 评估机制

Monitor Team 的最终评估结果是综合 SessionAnalyst 和 LearningProfiler 两个 Agent 的评价得出的。这种双层评估机制确保了:

1. **即时性与全面性的平衡**：SessionAnalyst 提供即时、具体的学习状态评估，而 LearningProfiler 则提供长期、全面的学习趋势分析
2. **多维度评价**：从参与度、理解度、知识点掌握、学习习惯等多个维度进行综合评估
3. **数据与洞察的结合**：SessionAnalyst 注重数据收集和实时分析，LearningProfiler 注重数据整合和深度洞察

两个 Agent 通过定期数据交互和评估校准，形成一个完整的学习监测与评估体系，不仅能够准确评估学习者的当前状态，还能预测学习潜力和发展方向。

# 3. 设计开发要求：
1. **Agent开发框架**：必须使用[Agno]作为开发框架，使用 deepwiki MCP 作为工具，查询[Agno]的Github仓库，从而掌握 Agent 的编程方法
2. **前后端分离**：深入分析本项目文件结构，将本项目重构为前后端架构，前端文件结构和视觉效果保持当前设计不变
3. 后端Agents与前端的通信，采用"AG-UI"协议开发，其知识库文档参照`@agui_guidance.txt`文档进行开发；
4. 设计可靠的前后端通信机制，可以使用`FastAPI` 实现



---

系统组成：

前端页面 (3个)：
Main Page (MP): 用户入口，输入主题，触发P1。
Course Planning Page (CPP): 显示P2，处理课程生成/加载，展示大纲和内容，触发ILP。
Interactive Learning Page (ILP): 用户与Teacher Agent互动学习，动态显示图片。
弹出页面 (2个)：
POPUP_1 (P1): 在MP上，用户进行三个选择后提交，进入CPP。
POPUP_2 (P2): 在CPP初始加载时，显示已有课程列表，引导用户选择。
后端 Agent (2个)：
Course Planner Agent (CPA): 接收课程大纲生成任务，返回大纲。
Content Designer Agent (CDA): 接收内容页面生成任务（基于大纲或加载已有内容），返回内容。
Teacher Agent (TA): 在ILP中，根据课程内容和用户问题生成回复。
核心流程：

MP: 用户在MP输入主题 -> 点击按钮 -> 出现P1。
P1: 用户在P1做3个选择 -> 点击提交 -> 进入CPP。
CPP (初始化): 进入CPP -> 出现P2（列出已有课程）。
P2 - 无相关课程: 用户点击“全新生成”。
P2 - 有相关课程: 用户可选择“使用当前课程”或“全新生成”。
CPP (课程生成/加载):
点击“全新生成”:
前端进入等待状态。
系统发送大纲生成任务给 CPA。
CPA返回大纲 -> 显示在CPP左侧。
系统发送内容生成任务给 CDA (基于新大纲)。
CDA返回内容 -> 显示在CPP右侧。
点击“使用当前课程”:
加载已有大纲和内容到CPP。
系统发送任务给 CDA 生成剩余内容 (如果需要)。
CPP (内容完成): 内容生成/加载完毕 -> 用户点击“开始学习” -> 跳转到ILP。
ILP:
用户与 Teacher Agent (TA) 通过对话区域互动，TA根据课程内容和用户问题生成回复。
Image Panel (ILP右侧) 根据交谈主题动态生成并显示图片。