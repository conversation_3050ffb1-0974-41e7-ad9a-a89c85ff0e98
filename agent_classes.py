from agno.agent.agent import Agent
from agno.tools.reasoning import ReasoningTools
from agno.models.chutes import Chutes
from agno.models.openai.chat import OpenAIChat
from agno.team import Team
import os, asyncio

# 设置环境变量
os.environ["CHUTES_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"

class ElementManager:
    def __init__(self):
        self.agent = Agent(
            name="Element Manager",
            agent_id="element_manager",
            role="Manage the element list",
            model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
            debug_mode=True,
            tools=[self.add_element, self.remove_element],
            session_state={"element_list": []},  # 在构造函数中初始化
            instructions=[
                "Manage the element list by adding and removing elements",
                "ONLY manage elements, NOT people",
                "Always confirm when elements are added or removed",
                "If the task is done, update the session state to log the changes & chores you've performed"
            ],
            show_tool_calls=True
        )

    def add_element(self, element: str) -> str:
        if element.lower() not in self.agent.session_state["element_list"]:
            self.agent.session_state["element_list"].append(element.lower())
            return f"Added `{element}` to the element list"
        else:
            return f"The element `{element}` is already in the list"

    def remove_element(self, element: str) -> str:
        for i, list_item in enumerate(self.agent.session_state["element_list"]):
            if list_item.lower() == element.lower():
                self.agent.session_state["element_list"].pop(i)
                return f"Removed `{list_item}` from the element list"
        return f"`{element}` was not found in the element list. Current element list: {self.agent.session_state['element_list']}"


class PeopleManager:
    def __init__(self):
        self.agent = Agent(
            name="People Manager",
            agent_id="people_manager",
            role="Manage the people list",
            model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
            debug_mode=False,
            tools=[self.add_people, self.remove_people],
            session_state={"people_list": []},  # 在构造函数中初始化
            instructions=[
                "Manage the people list by adding and removing people",
                "ONLY manage people, DO NOT manage any other items",
                "Always confirm when people are added or removed",
                "If the task is done, update the session state to log the changes & chores you've performed"
            ],
            show_tool_calls=True
        )

    def add_people(self, people: str) -> str:
        if people.lower() not in self.agent.session_state["people_list"]:
            self.agent.session_state["people_list"].append(people.lower())
            return f"Added `{people}` to the people list"
        else:
            return f"The people `{people}` is already in the list"

    def remove_people(self, people: str) -> str:
        for i, list_item in enumerate(self.agent.session_state["people_list"]):
            if list_item.lower() == people.lower():
                self.agent.session_state["people_list"].pop(i)
                return f"Removed `{list_item}` from the people list"
        return f"`{people}` was not found in the people list. Current people list: {self.agent.session_state['people_list']}"


class ManagementTeam:
    def __init__(self):
        self.team = Team(
            name="Manager Team",
            team_id="manager_team",
            mode="router",
            model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
            show_tool_calls=True,
            debug_mode=True,
            members=[ElementManager().agent, PeopleManager().agent],
            tools=[ReasoningTools(add_instructions=True)],
            session_state={"element_list": [], "people_list": []},
            instructions=[
                "Carefully thinking before you act, especially when you deal with complex tasks."
                "Choose to choose appropriate management agent to process different elements/people.",
                "Forward requests to add/remove elements and people to the Element Manager and People Manager, respectively!",
                "ONLY use the tools that are listed in the agent's tools list."
                "Finally, update the session state to log the changes & chores you've performed."
            ]
        )

    async def aprint_response(self, message: str, stream: bool = True) -> None:
        """异步处理和打印响应消息

        Args:
            message (str): 用户输入的消息
            stream (bool, optional): 是否流式输出. Defaults to True.
        """
        print(f"Team {self.team.name} is processing message: {message}")
        await self.team.aprint_response(message, stream=stream)

    async def arun_response(self, message: str) -> str:
        """异步处理并返回响应信息
        
        Args:
            message (str): 用户输入的消息
            
        Returns:
            str: 处理后的响应信息
        """
        print(f"Team {self.team.name} is processing message: {message}")
        try:
            # 使用 asyncio.wait_for 添加超时控制
            response = await asyncio.wait_for(
                self.team.arun(message, stream=False),  # 显式设置 stream=False
                timeout=60  # 设置 60 秒超时
            )
            return response
        except asyncio.TimeoutError:
            print("Request timed out after 60 seconds")
            return "Operation timed out"
        except Exception as e:
            print(f"Error occurred: {str(e)}")
            return f"Error: {str(e)}"
    
    def print_response(self, message: str, stream: bool = True) -> None:
        """同步处理和打印响应消息

        Args:
            message (str): 用户输入的消息
            stream (bool, optional): 是否流式输出. Defaults to True.
        """
        asyncio.run(self.aprint_response(message, stream))

    def show_lists(self) -> str:
        """显示当前元素列表和人员列表的状态
        
        Returns:
            str: 格式化的列表状态字符串
        """
        element_list = self.team.session_state.get("element_list", [])
        people_list = self.team.session_state.get("people_list", [])
        return f"Element list: {element_list}\nPeople list: {people_list}"

async def main():
    # 创建管理团队实例
    management_team = ManagementTeam()
    
    try:
        # 测试消息处理
        test_message = "Add milk, eggs, dog, Isaac Newton, Paul Dirac and James Clerk Maxwell to the appropriate list. Then randomly remove ONE people from the list. Finally, show the final items of the two lists in Python list format."
        print("\n=== Starting Test ===")
        
        # 记录初始状态
        initial_state = management_team.show_lists()
        print("Initial state:")
        print(initial_state)
        
        print("\nProcessing request...")
        # 使用 arun_response 获取响应并保存结果
        response = await management_team.arun_response(test_message)
        
        if response is None:
            print("No response received")
            return None
            
        # 记录最终状态
        final_state = management_team.show_lists()
        print("\nFinal state:")
        print(final_state)
        
        # 创建结果字典
        result = {
            'initial_state': initial_state,
            'message': test_message,
            'response': response,
            'final_state': final_state,
            'element_list': management_team.team.session_state.get('element_list', []),
            'people_list': management_team.team.session_state.get('people_list', [])
        }
        
        # 将结果保存到文件
        doc_path = os.path.join(os.getcwd(), 'doc')
        if not os.path.exists(doc_path):
            os.makedirs(doc_path)
            
        result_file = os.path.join(doc_path, 'management_team_result.txt')
        with open(result_file, 'w', encoding='utf-8') as f:
            for key, value in result.items():
                f.write(f"{key}:\n{value}\n\n")
        
        print(f"\nResults have been saved to: {result_file}")
        print("=== Test Complete ===")
        
        return result
        
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("Program completed successfully")
        else:
            print("Program completed with errors")
    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
    except Exception as e:
        print(f"Program terminated with error: {str(e)}")
