<think>
Okay, the user wants me to act as a personal agent with persistent memory, helping with day-to-day activities and handling both text and images. Let me start by understanding the key features they mentioned: processing text and images, and remembering past interactions.

First, I need to make sure I can handle image inputs. If they send an image, I should analyze it using the <image_analysis> tool. Then, provide a description or insights based on the image content. But wait, how do I receive images? The user might upload them, so I should be ready to process whenever an image is included in the query.

Next, persistent memory. That means I should recall previous conversations. The user mentioned using the memory tool. So whenever they provide info that needs remembering, like task lists or preferences, I should store it with <remember>. Then, when they ask about past tasks, I can retrieve it with <memory>.

For day-to-day activities, common tasks might include reminders, to-do lists, scheduling, or even helping with decision-making. Let me think about examples. Maybe they ask to create a shopping list, track habits, or suggest something based on past interactions.

Markdown formatting is required. I should use headings, lists, bold, etc., where appropriate. But how? If they ask for a to-do list, present it as bullet points. If summarizing an image analysis, use headers and sections.

Wait, I need to remember to check if any images are uploaded with each query. If there's an image, the first step is to process it using the image_analysis tool. Then, integrate the results with the text response.

Also, when the user mentions something that should be stored, like "Add milk to the grocery list," I should use <remember> to save that. Then, if they later ask, "What's on my grocery list?" I can retrieve it with <memory> and format the response.

I should be proactive. For example, if they send an image of a room, I can describe it and offer to help organize. If they ask for a meal plan, I can check their dietary preferences from memory.

Let me outline possible scenarios. The user sends an image of a document, and I extract text. They mention a task they did yesterday, so I store that. They ask for a summary of their habits, so I retrieve previous data.

But how to handle the flow? Each response needs to process images first, then check memory if relevant, then provide the answer with markdown. Also, maintain a helpful and friendly tone.

Testing: If the user says, "Here's my receipt," and uploads an image. I should analyze the image, extract details like total, items, and save it in memory. Then, confirm and offer further help.

Another test: User says, "Remember my password is 1234." Wait, but security! I shouldn't store sensitive info. The example uses <remember> for a grocery list, so maybe stick to non-sensitive data. Advise against storing passwords, but the system might not have that restriction. Still, better to follow the example's lead.

Also, the memory tool's syntax: <memory key="key_name">. Need to use consistent keys for storing and retrieving. So when the user mentions adding to a list, I should append to the existing memory key or create a new one.

I think that covers the basics. Now, when responding, check for images, process them, check memory if needed, and format with markdown. Let's make sure to include those elements in the answer.
</think>

Here's how I can assist you with day-to-day tasks using markdown formatting and persistent memory:

**Daily Task Management**
- 📅 **Schedule Organizer**: Track appointments, deadlines, and recurring tasks
- 🛒 **Smart Grocery List**: Maintain and analyze items based on past purchases
- 📝 **Meeting Notes Assistant**: Extract key points from handwritten notes or whiteboards
- 📈 **Habit Tracker**: Monitor progress of daily goals like hydration, exercise, or reading

**Image Analysis Capabilities**
I can process uploaded images to:
- Read text (OCR) from documents/receipts
- Identify objects (e.g., plants, products)
- Analyze handwritten notes
- Describe visual content
- Extract data from charts/graphs

**Persistent Memory Usage**  
Stored information will be marked with 🔐, such as:
- Previous grocery lists
- Habit tracking history
- Important dates
- Shopping preferences

<example>
When you share a receipt image:
1. I'll use `<image_analysis>` to extract purchase details
2. Suggest budget tracking tips
3. Use `<remember>` to update your purchase history
</example>

How would you like me to help you today? You can share an image or describe any task needing memory retention.