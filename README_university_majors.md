# 大学专业信息更新脚本

## 功能说明

这个脚本可以根据 `universities_list.xlsx` 中的大学列表，自动访问广东省教育考试院的联合招生院校专业查询网站 (https://www.eeagd.edu.cn/lzks/yxzycx/yxzycx.jsp)，获取每个大学的专业信息，并将结果保存到 `university_majors.xlsx` 文件中。

## 主要特性

1. **自动获取大学代码映射**：从目标网站获取所有大学的代码和名称映射关系
2. **智能匹配**：支持精确匹配和模糊匹配大学名称
3. **专业信息提取**：获取每个大学的批次、计划类别、科类、专业数量、计划数等信息
4. **错误处理**：包含SSL错误重试机制和网络异常处理
5. **增量更新**：跳过已经处理过的大学，支持断点续传
6. **详细日志**：提供完整的处理过程日志

## 文件结构

- `university_majors_updater.py` - 主脚本文件
- `universities_list.xlsx` - 输入文件，包含大学列表
- `university_majors.xlsx` - 输出文件，包含每个大学的专业信息（每个大学一个工作表）

## 输入文件格式

`universities_list.xlsx` 应包含以下列：
- `University Name` - 大学名称（必需）
- `Detail URL` - 详细URL（可选，脚本会忽略此列）

## 输出文件格式

`university_majors.xlsx` 包含多个工作表，每个大学一个工作表，包含以下列：
- `专业名称` - 专业类别信息（如：本科-普通类-文史类）
- `招生信息` - 专业数量和计划数信息
- `备注` - 招生范围等其他信息

## 使用方法

1. 确保已安装必要的Python包：
   ```bash
   pip install pandas requests beautifulsoup4 openpyxl
   ```

2. 准备输入文件 `universities_list.xlsx`

3. 运行脚本：
   ```bash
   python university_majors_updater.py
   ```

4. 查看输出文件 `university_majors.xlsx`

## 注意事项

1. **网络连接**：脚本需要访问互联网来获取数据
2. **请求频率**：脚本在每个请求之间添加了2秒延迟，避免对服务器造成过大压力
3. **SSL错误**：如果遇到SSL连接错误，脚本会自动重试最多3次
4. **数据准确性**：获取的数据基于目标网站的当前状态，可能会随时间变化

## 错误处理

脚本会处理以下类型的错误：
- 网络连接错误
- SSL证书错误
- 数据解析错误
- 文件读写错误

对于无法获取数据的大学，脚本会创建包含错误信息的占位符记录。

## 日志信息

脚本运行时会显示详细的日志信息，包括：
- 处理进度
- 成功获取的专业数量
- 错误信息
- 跳过的大学数量

## 示例输出

成功处理的大学会显示如下信息：
```
北京大学:
- 本科-普通类-文史类: 专业数: 23, 计划数: 3, 招生范围: 全部
- 本科-普通类-理工类: 专业数: 35, 计划数: 2, 招生范围: 全部
```

## 技术实现

- 使用 `requests` 库进行HTTP请求
- 使用 `BeautifulSoup` 解析HTML内容
- 使用 `pandas` 处理Excel文件
- 使用 `logging` 记录处理过程
