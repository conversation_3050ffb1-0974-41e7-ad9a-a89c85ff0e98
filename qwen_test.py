import asyncio, os
from textwrap import dedent
from agno.agent import Agent  
from agno.models.ollama import Ollama
from agno.models.xai import xAI
from agno.models.chutes import Chutes
from agno.models.openrouter.openrouter import OpenRouter
from agno.models.google.gemini import Gemini
from agno.tools.mcp import MCPTools, MultiMCPTools
from agno.tools.reasoning import ReasoningTools
# from agno.memory.v2 import Memory  # 注释掉 agno 的 Memory
from mem0 import Memory  # 使用 mem0 Memory 配合 Ollama embedding
from mem0 import MemoryClient



file_path = os.getcwd()
doc_path = os.path.join(file_path, 'doc')
if not os.path.exists(doc_path):
    os.makedirs(doc_path)

async def Chutes_agent(message: str) -> None:
    os.environ["CHUTES_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"
    
    # 配置 mem0 使用 Ollama 的 nomic embedding model 和 Ollama LLM
    config = {  
        "embedder": {  
            "provider": "ollama",  
            "config": {  
                "model": "nomic-embed-text",  
                "embedding_dims": 512,  
                "ollama_base_url": "http://localhost:11434"  
            }  
        },  
        "llm": {  
            "provider": "openai",  
            "config": {  
                "model": "Qwen3-235B-A22B",  
                "api_key": os.getenv("CHUTES_API_KEY"),  
                "openai_base_url": "https://llm.chutes.ai/v1",  # LiteLLM 使用 api_base  
                "temperature": 0.7,  
                "max_tokens": 4096  
            }  
        }  
    }
    
    # 创建 memory
    async with MultiMCPTools([
        # "fastmcp run mcp_server.py",
        # "npx -y @modelcontextprotocol/server-sequential-thinking",
        f"npx -y @modelcontextprotocol/server-filesystem {file_path}"
    ]) as multi_mcp_tools:
        # 使用 mem0 Memory 和 Ollama nomic embedding
        memory = Memory.from_config(config)

        agent = Agent(
            model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
            # 使用实例化后的multi_mcp_tools，而不是类名
            tools = [
                # multi_mcp_tools,
                ReasoningTools(
                    think=True,
                    analyze=True,
                    add_instructions=True
                )
            ],
            instructions = dedent(
                """
                分析问题并详细回答问题，生成有用的内容。
                """
            ),
            show_tool_calls=True,
            memory=memory,
            markdown=True
        )

    print(f"正在处理问题: {message}")
    await agent.aprint_response(message, stream=True)
    # print(memory.get_all())

# async def Grok_agent(message: str) -> None:
#     os.environ["OPENAI_API_KEY"] = "************************************************************************************"
    
#     async with MultiMCPTools([
#         # "fastmcp run mcp_server.py",  # 确保 mcp_server.py 在当前目录  
#         "fastmcp run mcp_server.py",
#         "npx -y @modelcontextprotocol/server-sequential-thinking",
#         f"npx -y @modelcontextprotocol/server-filesystem {file_path}"
#         ]) as multi_mcp_tools:
#             # 创建 memory
#             memory = Memory()

#             agent = Agent(
#                 model=xAI(id = "grok-3-beta"),
#                 tools = [
#                      multi_mcp_tools,
#                      ReasoningTools(
#                         think=True,
#                         analyze=True,
#                         add_instructions=True
#                     )
#                 ],
#                 instructions = dedent(
#                     """
#                     你需要使用 Sequential Thinking 的工具来分解复杂问题，首先思考问题各个方面，然后逐步解决。
#                     """
#                 ),
#                 show_tool_calls=True,
#                 memory=memory,
#                 markdown=True
#             )



#     print(f"正在处理问题: {message}")
#     await agent.aprint_response(message, stream=True)
#     # print(memory.get_all())

# model = Gemini(
#      id="gemini-2.5-pro-preview-05-06",
#      api_key="AIzaSyB6Ln0VUP8hn7IFV60aGwC0efPZZqpYcek"
# )

# async def Gemini_agent(message: str) -> None:
#     # os.environ["OPENAI_API_KEY"] = "e166ef72cb3c6d89d55a40008450661c8c339a586cff3bb71e2acabac7a4fcfd"

#     memory = Memory(model=model)

#     async with MultiMCPTools([
#         # "fastmcp run mcp_server.py",  # 确保 mcp_server.py 在当前目录  
#         "fastmcp run mcp_server.py",
#         "npx -y @modelcontextprotocol/server-sequential-thinking",
#         f"npx -y @modelcontextprotocol/server-filesystem {file_path}"
#         ]) as multi_mcp_tools:

#             agent = Agent(
#                 model=model,
#                 tools = [
#                      multi_mcp_tools,
#                      ReasoningTools(
#                         think=True,
#                         analyze=True,
#                         add_instructions=True
#                     )
#                 ],
#                 instructions = dedent(
#                     """
#                     你需要使用 Sequential Thinking 的工具来分解复杂问题，首先思考问题各个方面，然后逐步解决。
#                     """
#                 ),
#                 show_tool_calls=True,
#                 # memory=memory,
#                 markdown=True
#             )

#             print(f"正在处理问题: {message}")
#             await agent.aprint_response(message, stream=True)
#             # print(memory.get_all())


# async def Ollama_agent(message: str) -> None:
#     # 初始化 Ollama 模型  
#     model = Ollama(
#         id="qwen3:32b",  # 使用您本地安装的模型  
#         host="http://localhost:11434"  # Ollama 默认地址
#     )  

#     async with MultiMCPTools([
#         # "fastmcp run mcp_server.py",  # 确保 mcp_server.py 在当前目录  
#         "npx -y @modelcontextprotocol/server-sequential-thinking",
#         f"npx -y @modelcontextprotocol/server-filesystem {file_path}"
#     ]) as multi_mcp_tools:
        
#         # 创建 memory
#         memory = Memory()

#         # 创建 agent
#         agent = Agent(
#             model=model,  
#             tools= [
#                  multi_mcp_tools,
#                  ReasoningTools(
#                     think=True,
#                     analyze=True
#                     # add_instructions=True
#                 )
#             ],
#             memory=memory,
#             show_tool_calls=True,  
#             markdown=True
#         )
          
#         # 运行 agent 并打印响应  
#         print(f"正在处理问题: {message}")  
#         await agent.aprint_response(message, stream=True)  
  
if __name__ == "__main__":
    # 测试 agent  
    asyncio.run(Chutes_agent("""
                             ## 生成一个将Python编程知识融入物理学主题学习的项目式学习/问题式学习（PBL）课程大纲，以牛顿第二定律的发现为例；
                             ## 编程作为工具必须完全融入其中，不要单纯的教授Python语法知识，设计具体的物理问题实例来融入Python知识和编程问题；
                             ## 仅生成课程大纲，不要生成课程内容；
                             ## 课程大纲必须包含「课程目标」、「所有章节名称以及章节内容类型」、「章节内容」、
                             ## 「课程目标」：此课程内容概述、课程与课标对齐（NGSS, CSTA中查找相应对应项、 根据布鲁姆认知分层理论，与当前课程所培养的能力目标的对应情况）；
                             ## 「所有章节名称以及章节内容类型」：生成每个章节的题目和基本描述，用以为后续内容设计Agent提供指导，
                             ## 最后将回复内容全部写入{doc_path}这个路径下,如果已存在同名文件，则覆盖它；
                             ## 写入文件的内容必须要和回复内容保持严格一致，包含所有公式、代码等元素。
                             ## 生成的课程大纲控制在4000字以内，代码不要超过3段！
                             """))
