import asyncio, os
import base64
from textwrap import dedent
from typing import Optional

from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.models.xai import xAI
from agno.models.chutes import Chutes


from agno.tools.mcp import MCPTools, MultiMCPTools
from agno.tools.reasoning import ReasoningTools
from agno.utils.pprint import apprint_run_response

from mem0 import Memory


# Create local documentation folder
file_path = os.getcwd()
doc_path = os.path.join(file_path, 'doc')
if not os.path.exists(doc_path):
    os.makedirs(doc_path)

os.environ["CHUTES_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"

# 配置 mem0 Memory 实例（本地）  
config = {  
    "embedder": {  
        "provider": "ollama",  
        "config": {  
            "model": "nomic-embed-text",  
            "ollama_base_url": "http://localhost:11434"  
        }  
    },  
    "llm": {  
        "provider": "openai",  
        "config": {  
            "model": "Qwen/Qwen3-235B-A22B",  
            "openai_base_url": "https://llm.chutes.ai/v1",  
            "api_key": os.getenv("CHUTES_API_KEY"),  
            "temperature": 0.1,
            "max_tokens": 4096  
        }  
    },
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "integration_test",
            "path": "./chroma_db"
        }
    }
}  
  
# 初始化本地 Memory 实例  
memory = Memory.from_config(config)  

# 初始化输入计数器
input_counter = 0
  
# 定义 Agno agent  
agent = Agent(
    name="Personal Agent with Memory",  
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    description="You are a helpful personal agent with persistent memory that helps with day-to-day activities. "  
                "You can process both text and images and remember past interactions.",  
    markdown=True,
    save_response_to_file="doc/{name}_{session_id}_{run_id}.txt"
)
  
async def chat_user(
    user_input: Optional[str] = None,  
    user_id: str = "", 
    image_path: Optional[str] = None  
) -> str:
    """  
    处理用户输入，支持文本和图像，集成 mem0 Memory 进行记忆管理。  
  
    Args:  
        user_input: 用户的文本输入  
        user_id: 用户唯一标识符  
        image_path: 图像文件路径（可选）  
  
    Returns:  
        Agent 的响应字符串  
    """  
    global input_counter
    
    # 初始化消息列表
    messages = []
      
    # 处理图像输入  
    if image_path:  
        # 将图像转换为 base64  
        with open(image_path, "rb") as image_file:  
            base64_image = base64.b64encode(image_file.read()).decode("utf-8")  
  
        # 添加文本消息（如果有）
        if user_input:  
            messages.append({  
                "role": "user",  
                "content": user_input  
            })  
  
        # 添加图像消息
        messages.append({  
            "role": "user",   
            "content": {  
                "type": "image_url",  
                "image_url": {  
                    "url": f"data:image/jpeg;base64,{base64_image}"  
                }  
            }  
        })  
  
        # 将消息存储到 Memory 中  
        memory.add(messages, user_id=user_id)  
        print("✅ 图像和文本已存储到本地记忆中")  
    
    # 处理纯文本输入
    if user_input:
        # 更新输入计数器
        input_counter += 1
        print(f"📊 输入计数器: {input_counter}")
        
        # 如果还没有处理过图像，创建文本消息
        if not image_path:
            messages.append({
                "role": "user",
                "content": user_input
            })

            memory.add(messages, user_id=user_id)
            print("✅ 文本已存储到本地记忆中...")

        # 搜索相关记忆 
        memories = memory.search(user_input, user_id=user_id)  
        
        # 修复记忆上下文构建
        if memories and isinstance(memories, dict) and 'results' in memories:
            memory_list = memories['results']
            memory_context = "\n".join(f"- {m.get('memory', str(m))}" for m in memory_list)
        elif memories and isinstance(memories, list):
            memory_context = "\n".join(f"- {m.get('memory', str(m))}" for m in memories)
        else:
            memory_context = ""  
  
        # 构建包含记忆上下文的提示  
        prompt = f"""  
你是一个有持久记忆的个人助手，帮助用户处理日常活动并记住所有重要信息。  
  
你的任务是：  
1. 分析给定的图像（如果有）并提取有意义的细节来回答用户的问题  
2. 使用你对用户的过往记忆来个性化你的回答  
3. 结合图像内容和记忆生成有用的、上下文感知的响应  

我记住的关于用户的信息：  
{memory_context}  
  
用户问题：  
{user_input}  
"""  
  
        # 使用增强版 print 功能 - 流式输出响应
        print(f"✅ 交互已存储到本地记忆中")
        print(f"👨🏻‍🦱 用户ID: {user_id}")
        print(f"🤖 用户请求: {user_input}")
        print("\n🤖 Agent 响应:")
        
        # 直接使用 aprint_response 处理提示并获取响应
        # response_stream = await agent.arun(message=prompt, stream=True)
        response_stream = await agent.aprint_response(prompt, stream=True)

        # await apprint_run_response(response_stream, markdown=True)
        # await agent.aprint_response(response_stream, stream=True)
        
        # 将交互存储到记忆中  
        interaction = f"用户: {user_input}\n助手: {response_stream}"  
        memory.add(interaction, user_id=user_id)  
        
        # 返回响应内容  
        return response_stream
  
    return "用户没有提供用户输入或图像。"  

def get_input_counter() -> int:
    """获取当前输入计数器的值"""
    return input_counter

def reset_input_counter() -> None:
    """重置输入计数器"""
    global input_counter
    input_counter = 0
    print("🔄 输入计数器已重置")
  
async def Chutes_agent(user_id: str,  user_query: str):  
    """  
    主要的 agent 函数，处理用户查询  
    """  

    try:  
        # 处理用户查询  
        response = await chat_user(
            user_input=user_query,
            user_id=user_id
        )
          
        print(f"Agent 响应: {response}")
          
        # 可选：显示当前用户的所有记忆  
        try:
            all_memories = memory.get_all(user_id=user_id)
            if isinstance(all_memories, list):
                memory_count = len(all_memories)
                print(f"\n当前用户记忆数量: {memory_count}")
            elif isinstance(all_memories, dict):
                if 'results' in all_memories:
                    memory_count = len(all_memories['results'])
                    print(f"\n当前用户记忆数量: {memory_count}")
                else:
                    print(f"\n记忆数据结构: {list(all_memories.keys())}")
            else:
                print(f"\n记忆数据类型: {type(all_memories)}")
        except Exception as memory_error:
            print(f"\n⚠️  获取记忆时出错: {memory_error}")
            # 尝试简单计数
            try:
                search_result = memory.search("", user_id=user_id, limit=100)
                if isinstance(search_result, list):
                    print(f"通过搜索估算记忆数量: {len(search_result)}")
                elif isinstance(search_result, dict) and 'results' in search_result:
                    print(f"通过搜索估算记忆数量: {len(search_result['results'])}")
            except:
                print("无法获取记忆统计信息")
        

        print(f"📊 总输入次数: {input_counter}")
        print(f"\nFINAL RESPONSE: {response}")
        return response  
          
    except Exception as e:  
        print(f"错误: {e}")  
        return f"处理请求时发生错误: {e}"  
    
    
  
# 使用示例  
if __name__ == "__main__":
    
    async def run_examples():
        # 示例 1: 纯文本交互  
        print("=== 示例 1: 文本交互 ===")  
        response1 = await chat_user(  
            "我喜欢喝咖啡，特别是生椰拿铁",  
            user_id="freemank"  
        )  
        print(f"响应: {response1}")  
          
        # 示例 2: 后续交互（测试记忆）  
        print("\n=== 示例 2: 测试记忆 ===")
        for i in range(1):
            response2 = await chat_user(
                f"在地球形成的第{20*(i+1)}亿年，地球环境产生了什么变化？单纯从地质学角度回答上述问题即可，不需要考虑我口味问题。",
                user_id="freemank"
            )

            print(f"响应: {response2}")  
          
        # 示例 3: 图像 + 文本交互  
        print("\n=== 示例 3: 多模态交互 ===")  
        # response3 = await chat_user(  
        #     "这是我今天的早餐",  
        #     image_path="breakfast.jpg",  # 确保图像文件存在  
        #     user_id="user_123"  
        # )  
        # print(f"响应: {response3}")  
          
        # 示例 4: 异步调用  
        print("\n=== 示例 4: 异步调用 ===")  
        await Chutes_agent("freemank", "请分析一下，在那几个时期，地球上有我喜欢的那个饮品吗？")
    
    # 运行所有示例
    asyncio.run(run_examples())