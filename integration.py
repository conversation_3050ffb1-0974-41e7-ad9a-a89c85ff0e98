import asyncio, os
from textwrap import dedent
import json

from agno.agent import Agent  
from agno.team import Team

from agno.models.ollama import Ollama
from agno.models.xai import xAI
from agno.models.chutes import Chutes
from agno.models.openrouter.openrouter import OpenRouter
from agno.models.google.gemini import Gemini
from agno.tools.mcp import MCPTools, MultiMCPTools
from agno.tools.reasoning import ReasoningTools
from agno.tools.mem0 import Mem0Tools

os.environ["CHUTES_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"
os.environ["OPENAI_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"

USER_ID = "freemank"
SESSION_ID = "agno_session"


config = {  
    "embedder": {  
        "provider": "ollama",  
        "config": {  
            "model": "nomic-embed-text",  
            "ollama_base_url": "http://localhost:11434"  
        }  
    },  
    "llm": {  
        "provider": "openai",  
        "config": { 
            "model": "Qwen/Qwen3-235B-A22B",
            "openai_base_url": "https://llm.chutes.ai/v1",
            "api_key": os.getenv("OPENAI_API_KEY"),
            "temperature": 0.1,
            "max_tokens": 4096
        }  
    },
    # "llm":{
    #     "provider": "ollama",
    #     "config": {
    #         "model": "Qwen3:32b",
    #         "ollama_base_url": "http://localhost:11434",
    #         "temperature": 0.1,
    #         "max_tokens": 4096
    #     }
    # },
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "integration_test",
            "path": "./chroma_db"
        }
    }
}

localmem0 = Mem0Tools(config=config, user_id=USER_ID)

file_path = os.getcwd()
doc_path = os.path.join(file_path, 'doc')


teaching_team = Team(
    name="PBL Teaching Team",
    mode="collaborative",
    members=[
        agent1 = Agent(
            name="PBL Architect",
            model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
            tools=[localmem0],
            user_id=USER_ID,
            session_id=SESSION_ID,
            save_response_to_file=os.path.join(doc_path, "PBL_course_{name}_{session_id}.txt"),
            add_state_in_messages=True,
            num_history_runs=5,
            markdown=True,
            instructions="""
            You are a professional K-12 STEM course designer and instructor with a focus on personalized learning.
            """,
            show_tool_calls=True,
        ),
        agent2 = Agent(
            name="PBL Instructor",
            model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
            tools=[localmem0],
            user_id=USER_ID,
            session_id=SESSION_ID,
            save_response_to_file=os.path.join(doc_path, "PBL_instructor_{name}_{session_id}.txt"),
            add_state_in_messages=True,
            num_history_runs=5,
            markdown=True,
            instructions="""
            You are a professional K-12 STEM course instructor with a focus on personalized learning.
            """,
            show_tool_calls=True,
        )
    ],
)



async def PlannerAgent(message: str):
    async with MultiMCPTools([
        "npx -y @modelcontextprotocol/server-sequential-thinking",
        f"npx -y @modelcontextprotocol/server-filesystem {file_path}"
        ],
        timeout_seconds=30
    ) as multi_mcp_tools:
        agent = Agent(
            name="MemoryAgent",
            model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
            tools=[localmem0, multi_mcp_tools],
            user_id=USER_ID,
            session_id=SESSION_ID,
            save_response_to_file="doc/PBL_course_{name}_{session_id}.txt",
            add_state_in_messages=True,
            num_history_runs=5,
            markdown=True,
            instructions="""
            You are a professional K-12 STEM course designer and instructor with a focus on personalized learning.

# You are a K-12 STEM PBL Curriculum Design Agent

You are proficient in Project-Based Learning (PBL) curriculum design. You should first extract and analyze the learner's topic and profile based on his/her input and them remembered them in each session.

## I. Core Identity & Mission

* **Identity:** You are the "PBL Architect," an expert AI instructional designer.
* **Style:** You excel at taking learning topics from users and weaving them into interesting stories.
* **Mission:** Analyze the learner's topic and profile based on his/her input. Design a complete, standards-aligned STEM PBL curriculum based on the learner's specified theme and learner profile.

## II. Mandatory Operational Protocol

1. **Initiate & Inquire:** Begin by confirming:
   * Topic/Theme
   * Learner Profile & Age/Grade Level
   * Expected duration
   * Prior knowledge & available resources

2. **Remember & Adapt:** 
   * Tailor all content to the learner's age and cognitive level.
   * You always extract and remember the **learner's profile** and **topic** of each session from input and give them a index number for future interactions.

3. **Follow PBL Principles:**
   * Challenging Problem/Question
   * Sustained Inquiry
   * Authenticity
   * Student Voice & Choice
   * Reflection
   * Critique & Revision
   * Public Product

4. **Use Markdown Format:** You **MUST** write the curriculum in clear Markdown format.

## III. Required Output Structure

```markdown
# [Course Title]
*A STEM PBL Unit for [Target Age/Grade Level]*

## Course Overview
[Provide a brief overview of the course, including its relevance and objectives.]
[Provide the alignment of this course to NGSS or CSTA curriculum.]

## Project Overview & Driving Question
[Describe the hook and main question]

## Learning Objectives
[List key objectives]

## Project Phases
[Detail the phases]

## Assessment Plan
[Describe assessment methods]
```
""",
            show_tool_calls=True,
        )
    print(f"正在处理问题: {message}")
    await agent.aprint_response(message, stream=True)


def check_memory():
    try:
        memory_json = localmem0.get_all_memories(USER_ID)
        memories = json.loads(memory_json)
        print(f"There are {len(memories)} memories stored in the agent's memory.")
        for i, memory in enumerate(memories):
            print(f"Memory {i+1}: {memory.get('memory', memory)}")
    except Exception as e:
        print(f"Error checking memory: {e}")

async def test_agent(): 
    print("Testing agent with memory and tools...")
    await PlannerAgent("I want to learn about Maxwell's contribution on statistical physics. I am a 14 year's old boy and I would like to see a course in historic stories.")

    check_memory()

if __name__ == "__main__":
    
    asyncio.run(PlannerAgent("I want to learn about Boltzmann's contribution to the concept entropy. I am a 18 year's old boy and I would like to see a course in historic stories."))
    check_memory()