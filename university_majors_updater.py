import pandas as pd
import os
import requests
from bs4 import BeautifulSoup
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Path to the list of universities
uni_list_file = "universities_list.xlsx"

# Output Excel file path
output_file = "university_majors.xlsx"

def clean_url(url_data):
    """清理URL数据，处理可能的WebElement对象或其他格式"""
    if pd.isna(url_data):
        return None

    url_str = str(url_data)

    # 如果包含WebElement信息，尝试提取实际URL
    if 'WebElement' in url_str:
        # 这种情况下URL数据可能损坏，返回None
        return None

    # 简单的URL验证
    if url_str.startswith(('http://', 'https://')):
        return url_str

    return None

def fetch_university_majors(url, university_name):
    """
    从给定URL获取大学专业信息
    这是一个示例函数，需要根据实际网站结构进行调整
    """
    if not url:
        logger.warning(f"No valid URL for {university_name}")
        return None

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        logger.info(f"Fetching data for {university_name} from {url}")
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # 这里需要根据实际网站结构来解析专业信息
        # 以下是一个通用的示例，需要根据具体网站调整
        majors_data = []

        # 示例：查找包含专业信息的表格或列表
        # 这需要根据实际网站结构进行调整
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows[1:]:  # 跳过表头
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    major_name = cells[0].get_text(strip=True)
                    additional_info = cells[1].get_text(strip=True) if len(cells) > 1 else ""

                    if major_name and major_name not in ['专业名称', '专业', '学科']:
                        majors_data.append([major_name, additional_info, ""])

        # 如果没有找到表格，尝试查找列表
        if not majors_data:
            lists = soup.find_all(['ul', 'ol'])
            for ul in lists:
                items = ul.find_all('li')
                for item in items:
                    text = item.get_text(strip=True)
                    if text and len(text) > 2:  # 过滤太短的文本
                        majors_data.append([text, "", ""])

        # 如果仍然没有数据，创建一个占位符
        if not majors_data:
            majors_data = [["需要手动更新", "未能自动提取专业信息", f"URL: {url}"]]

        return majors_data

    except requests.RequestException as e:
        logger.error(f"Error fetching data for {university_name}: {e}")
        return [["网络错误", f"无法访问URL: {str(e)}", f"URL: {url}"]]
    except Exception as e:
        logger.error(f"Error parsing data for {university_name}: {e}")
        return [["解析错误", f"无法解析网页内容: {str(e)}", f"URL: {url}"]]

def update_excel_file():
    """更新Excel文件，遍历所有大学并获取专业信息"""

    # 读取大学列表
    try:
        uni_df = pd.read_excel(uni_list_file)
        logger.info(f"Loaded {len(uni_df)} universities from {uni_list_file}")
        logger.info(f"Columns: {uni_df.columns.tolist()}")
    except Exception as e:
        logger.error(f"Error reading {uni_list_file}: {e}")
        return

    # 检查必要的列是否存在
    if 'University Name' not in uni_df.columns:
        logger.error("'University Name' column not found in the Excel file")
        return

    url_column = 'Detail URL' if 'Detail URL' in uni_df.columns else 'Detailed URL'
    if url_column not in uni_df.columns:
        logger.error(f"Neither 'Detail URL' nor 'Detailed URL' column found in the Excel file")
        return

    # 检查现有的输出文件
    existing_sheets = []
    if os.path.exists(output_file):
        try:
            with pd.ExcelFile(output_file) as xls:
                existing_sheets = xls.sheet_names
                logger.info(f"Found existing file with {len(existing_sheets)} sheets")
        except Exception as e:
            logger.error(f"Error reading existing file: {e}")

    # 过滤掉无效的大学名称
    valid_universities = uni_df[
        (uni_df['University Name'].notna()) &
        (uni_df['University Name'] != '院校代码') &
        (uni_df['University Name'].str.len() > 1)
    ].copy()

    logger.info(f"Found {len(valid_universities)} valid universities to process")

    # 创建Excel写入器
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl', mode='a' if os.path.exists(output_file) else 'w', if_sheet_exists='replace') as writer:

            processed_count = 0
            skipped_count = 0

            for _, row in valid_universities.iterrows():
                university_name = row['University Name']
                detail_url = clean_url(row[url_column])

                # 检查是否已经处理过这个大学
                if university_name in existing_sheets:
                    logger.info(f"Skipping {university_name} - already exists")
                    skipped_count += 1
                    continue

                logger.info(f"Processing {university_name} ({processed_count + 1}/{len(valid_universities)})")

                # 获取专业信息
                majors_data = fetch_university_majors(detail_url, university_name)

                if majors_data:
                    # 创建DataFrame
                    majors_df = pd.DataFrame(majors_data, columns=["专业名称", "招生信息", "备注"])

                    # 写入Excel
                    try:
                        majors_df.to_excel(writer, sheet_name=university_name, index=False)
                        logger.info(f"Successfully added {len(majors_data)} majors for {university_name}")
                        processed_count += 1
                    except Exception as e:
                        logger.error(f"Error writing data for {university_name}: {e}")
                else:
                    # 创建占位符
                    placeholder_df = pd.DataFrame([["待收集", "无法获取数据", f"URL: {detail_url}"]],
                                                columns=["专业名称", "招生信息", "备注"])
                    try:
                        placeholder_df.to_excel(writer, sheet_name=university_name, index=False)
                        logger.info(f"Created placeholder for {university_name}")
                        processed_count += 1
                    except Exception as e:
                        logger.error(f"Error creating placeholder for {university_name}: {e}")

                # 添加延迟以避免过于频繁的请求
                time.sleep(1)

                # 每处理10个大学后显示进度
                if (processed_count + skipped_count) % 10 == 0:
                    logger.info(f"Progress: {processed_count + skipped_count}/{len(valid_universities)} universities processed")

            logger.info(f"Completed! Processed: {processed_count}, Skipped: {skipped_count}")

    except Exception as e:
        logger.error(f"Error creating Excel writer: {e}")

if __name__ == "__main__":
    update_excel_file()
    print(f"Excel file {output_file} has been updated with university majors data.")
