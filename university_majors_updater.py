import pandas as pd
import os

# Path to the list of universities
uni_list_file = "universities_list.xlsx"

# Output Excel file path
output_file = "university_majors.xlsx"

def update_excel_file():
    # Read the list of universities
    uni_df = pd.read_excel(uni_list_file)
    print(f"Loaded {len(uni_df)} universities from {uni_list_file}")

    # Check if the output file already exists
    if os.path.exists(output_file):
        # Load existing Excel file
        with pd.ExcelFile(output_file) as xls:
            existing_sheets = xls.sheet_names
            print(f"Existing sheets in {output_file}: {existing_sheets}")
    else:
        existing_sheets = []
        print(f"Creating new file {output_file}")

    # Create Excel writer object
    with pd.ExcelWriter(output_file, engine='openpyxl', mode='a' if os.path.exists(output_file) else 'w', if_sheet_exists='replace') as writer:
        # For demonstration, this script assumes data for Peking University is already available
        # In a real scenario, you would replace this with actual data collection logic
        if '北京大学' not in existing_sheets:
            # Example data for Peking University (replace with actual data)
            peking_data = [
                ["专业名称1", "招生人数1", "其他信息1"],
                ["专业名称2", "招生人数2", "其他信息2"],
                # ... add all 23 majors here
            ]
            peking_df = pd.DataFrame(peking_data, columns=["专业名称", "招生人数", "其他信息"])
            peking_df.to_excel(writer, sheet_name='北京大学', index=False)
            print("Added data for 北京大学")
        else:
            print("Sheet for 北京大学 already exists, skipping...")

        # Placeholder for other universities
        for uni_name in uni_df["University Name"]:
            if uni_name != '北京大学' and uni_name not in existing_sheets:
                # Placeholder sheet for other universities (to be updated with actual data)
                placeholder_df = pd.DataFrame([["待收集", "待收集", "待收集"]], columns=["专业名称", "招生人数", "其他信息"])
                placeholder_df.to_excel(writer, sheet_name=uni_name, index=False)
                print(f"Created placeholder sheet for {uni_name}")
            else:
                print(f"Sheet for {uni_name} already exists or is 北京大学, skipping...")

if __name__ == "__main__":
    update_excel_file()
    print(f"Excel file {output_file} has been updated with placeholders for all universities. Please manually update the data for each university following the format used for 北京大学.")
