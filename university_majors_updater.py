import pandas as pd
import os
import requests
from bs4 import BeautifulSoup
import time
import logging
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Path to the list of universities
uni_list_file = "universities_list.xlsx"

# Output Excel file path
output_file = "university_majors_detailed.xlsx"

# 目标网站URL
BASE_URL = "https://www.eeagd.edu.cn/lzks/yxzycx/yxzycx.jsp"
DETAIL_BASE_URL = "https://www.eeagd.edu.cn/lzks/yxzycx/yxzy.jsp"

def get_university_details_from_main_page():
    """
    从主页面获取所有大学的详细信息，包括"查看专业"链接的参数
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        logger.info("Fetching university details from main page...")
        response = requests.get(BASE_URL, headers=headers, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')
        university_details = {}

        # 查找包含大学信息的表格（第二个表格）
        tables = soup.find_all('table')
        if len(tables) >= 2:
            university_table = tables[1]
            rows = university_table.find_all('tr')

            # 跳过表头，处理数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 9:
                    university_code = cells[0].get_text(strip=True)
                    university_name = cells[1].get_text(strip=True)
                    batch = cells[2].get_text(strip=True)
                    plan_category = cells[3].get_text(strip=True)
                    subject_category = cells[4].get_text(strip=True)

                    # 查找"查看专业"链接
                    last_cell = cells[-1]
                    links = last_cell.find_all('a')

                    for link in links:
                        onclick = link.get('onclick', '')
                        if 'getYxzy' in onclick:
                            # 解析onclick参数: getYxzy('10001','11','00','1')
                            match = re.search(r"getYxzy\('([^']+)','([^']+)','([^']+)','([^']+)'\)", onclick)
                            if match:
                                yx_h, pc_h, jhlb_h, zykl_h = match.groups()

                                # 创建唯一键
                                key = f"{university_name}_{subject_category}"

                                university_details[key] = {
                                    'university_code': university_code,
                                    'university_name': university_name,
                                    'batch': batch,
                                    'plan_category': plan_category,
                                    'subject_category': subject_category,
                                    'detail_params': {
                                        'yx_h': yx_h,
                                        'pc_h': pc_h,
                                        'jhlb_h': jhlb_h,
                                        'zykl_h': zykl_h
                                    }
                                }

        logger.info(f"Found {len(university_details)} university-category combinations")
        return university_details

    except Exception as e:
        logger.error(f"Error getting university details: {e}")
        return {}

def fetch_university_majors_from_detail_page(detail_params, university_name, subject_category):
    """
    从专业详情页面获取具体的专业信息
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': BASE_URL
        }

        # 构建详情页面URL
        detail_url = f"{DETAIL_BASE_URL}?yx_h={detail_params['yx_h']}&pc_h={detail_params['pc_h']}&jhlb_h={detail_params['jhlb_h']}&zykl_h={detail_params['zykl_h']}"

        logger.info(f"Fetching detailed majors for {university_name} ({subject_category}) from {detail_url}")

        # 添加重试机制来处理SSL错误
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(detail_url, headers=headers, timeout=30)
                response.raise_for_status()
                break
            except requests.exceptions.SSLError as e:
                if attempt < max_retries - 1:
                    logger.warning(f"SSL error for {university_name}, retrying... (attempt {attempt + 1})")
                    time.sleep(5)  # 等待5秒后重试
                    continue
                else:
                    raise e

        soup = BeautifulSoup(response.content, 'html.parser')
        majors_data = []

        # 查找专业信息表格（第二个表格包含具体专业信息）
        tables = soup.find_all('table')
        if len(tables) >= 2:
            majors_table = tables[1]  # 第二个表格包含专业详情
            rows = majors_table.find_all('tr')

            # 跳过表头，处理数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 7:  # 确保有足够的列
                    university_code = cells[0].get_text(strip=True)
                    university_name_cell = cells[1].get_text(strip=True)
                    major_code = cells[2].get_text(strip=True)
                    major_name = cells[3].get_text(strip=True)
                    major_batch = cells[4].get_text(strip=True)
                    plan_category = cells[5].get_text(strip=True)
                    subject_type = cells[6].get_text(strip=True)

                    # 过滤掉无效数据
                    if major_name and major_name not in ['招生专业', '专业名称']:
                        majors_data.append([
                            major_name,
                            major_code,
                            major_batch,
                            plan_category,
                            subject_type,
                            university_code,
                            university_name_cell
                        ])

        # 如果没有找到专业数据，创建占位符
        if not majors_data:
            majors_data = [["未找到专业信息", "", "", "", subject_category, detail_params['yx_h'], university_name]]

        logger.info(f"Found {len(majors_data)} specific majors for {university_name} ({subject_category})")
        return majors_data

    except Exception as e:
        logger.error(f"Error fetching detailed majors for {university_name}: {e}")
        return [["获取失败", "", "", "", subject_category, detail_params.get('yx_h', ''), f"错误: {str(e)}"]]

def update_excel_file():
    """更新Excel文件，遍历所有大学并获取专业信息"""

    # 首先获取目标网站的大学详细信息
    university_details = get_university_details_from_main_page()
    if not university_details:
        logger.error("Failed to get university details from target website")
        return

    # 读取大学列表
    try:
        uni_df = pd.read_excel(uni_list_file)
        logger.info(f"Loaded {len(uni_df)} universities from {uni_list_file}")
        logger.info(f"Columns: {uni_df.columns.tolist()}")
    except Exception as e:
        logger.error(f"Error reading {uni_list_file}: {e}")
        return

    # 检查必要的列是否存在
    if 'University Name' not in uni_df.columns:
        logger.error("'University Name' column not found in the Excel file")
        return

    # 检查现有的输出文件
    existing_sheets = []
    if os.path.exists(output_file):
        try:
            with pd.ExcelFile(output_file) as xls:
                existing_sheets = xls.sheet_names
                logger.info(f"Found existing file with {len(existing_sheets)} sheets")
        except Exception as e:
            logger.error(f"Error reading existing file: {e}")

    # 过滤掉无效的大学名称并去除重复
    valid_universities = uni_df[
        (uni_df['University Name'].notna()) &
        (uni_df['University Name'] != '院校代码') &
        (uni_df['University Name'].str.len() > 1)
    ].drop_duplicates(subset=['University Name']).copy()

    logger.info(f"Found {len(valid_universities)} valid universities to process")

    # 创建Excel写入器
    try:
        if os.path.exists(output_file):
            with pd.ExcelWriter(output_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                process_universities_new(writer, valid_universities, existing_sheets, university_details)
        else:
            with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
                process_universities_new(writer, valid_universities, existing_sheets, university_details)

    except Exception as e:
        logger.error(f"Error creating Excel writer: {e}")

def process_universities_new(writer, valid_universities, existing_sheets, university_details):
    """处理大学列表并写入Excel - 新版本，获取具体专业信息"""
    processed_count = 0
    skipped_count = 0
    not_found_count = 0

    for _, row in valid_universities.iterrows():
        university_name = row['University Name']

        # 检查是否已经处理过这个大学
        if university_name in existing_sheets:
            logger.info(f"Skipping {university_name} - already exists")
            skipped_count += 1
            continue

        logger.info(f"Processing {university_name} ({processed_count + skipped_count + not_found_count + 1}/{len(valid_universities)})")

        # 查找该大学的所有专业类别
        university_matches = []
        for _, details in university_details.items():
            if details['university_name'] == university_name:
                university_matches.append(details)

        # 如果没有精确匹配，尝试模糊匹配
        if not university_matches:
            for _, details in university_details.items():
                if university_name in details['university_name'] or details['university_name'] in university_name:
                    university_matches.append(details)
                    logger.info(f"Found fuzzy match: {university_name} -> {details['university_name']}")

        if university_matches:
            all_majors_data = []

            # 为每个专业类别获取详细信息
            for match in university_matches:
                subject_category = match['subject_category']
                detail_params = match['detail_params']

                logger.info(f"Fetching {subject_category} majors for {university_name}")
                majors_data = fetch_university_majors_from_detail_page(detail_params, university_name, subject_category)

                # 将数据添加到总列表中
                all_majors_data.extend(majors_data)

                # 添加延迟避免请求过于频繁
                time.sleep(1)

            if all_majors_data:
                # 创建DataFrame
                majors_df = pd.DataFrame(all_majors_data, columns=[
                    "专业名称", "专业代码", "专业批次", "计划类别", "招生科类", "院校代码", "院校名称"
                ])

                # 写入Excel
                try:
                    majors_df.to_excel(writer, sheet_name=university_name, index=False)
                    logger.info(f"Successfully added {len(all_majors_data)} majors for {university_name}")
                    processed_count += 1
                except Exception as e:
                    logger.error(f"Error writing data for {university_name}: {e}")
            else:
                # 创建占位符
                placeholder_df = pd.DataFrame([["待收集", "", "", "", "", "", "无法获取专业数据"]],
                                            columns=["专业名称", "专业代码", "专业批次", "计划类别", "招生科类", "院校代码", "院校名称"])
                try:
                    placeholder_df.to_excel(writer, sheet_name=university_name, index=False)
                    logger.info(f"Created placeholder for {university_name}")
                    processed_count += 1
                except Exception as e:
                    logger.error(f"Error creating placeholder for {university_name}: {e}")
        else:
            # 没有找到对应的大学
            logger.warning(f"University not found in website data: {university_name}")
            placeholder_df = pd.DataFrame([["未找到院校", "", "", "", "", "", "目标网站中未找到该大学"]],
                                        columns=["专业名称", "专业代码", "专业批次", "计划类别", "招生科类", "院校代码", "院校名称"])
            try:
                placeholder_df.to_excel(writer, sheet_name=university_name, index=False)
                not_found_count += 1
            except Exception as e:
                logger.error(f"Error creating not-found placeholder for {university_name}: {e}")

        # 添加延迟以避免过于频繁的请求
        time.sleep(2)

        # 每处理10个大学后显示进度
        if (processed_count + skipped_count + not_found_count) % 10 == 0:
            logger.info(f"Progress: {processed_count + skipped_count + not_found_count}/{len(valid_universities)} universities processed")

    logger.info(f"Completed! Processed: {processed_count}, Skipped: {skipped_count}, Not found: {not_found_count}")

if __name__ == "__main__":
    update_excel_file()
    print(f"Excel file {output_file} has been updated with university majors data.")
