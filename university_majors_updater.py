import pandas as pd
import os
import requests
from bs4 import BeautifulSoup
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Path to the list of universities
uni_list_file = "universities_list.xlsx"

# Output Excel file path
output_file = "university_majors.xlsx"

# 目标网站URL
BASE_URL = "https://www.eeagd.edu.cn/lzks/yxzycx/yxzycx.jsp"

def get_university_code_mapping():
    """
    从目标网页获取大学代码和名称的映射关系
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        logger.info("Fetching university code mapping from target website...")
        response = requests.get(BASE_URL, headers=headers, timeout=30)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # 查找院校下拉选择框
        university_mapping = {}

        # 查找包含大学信息的select元素或其他结构
        # 根据网页结构，大学信息可能在select选项中
        select_elements = soup.find_all('select')
        for select in select_elements:
            options = select.find_all('option')
            for option in options:
                value = option.get('value', '').strip()
                text = option.get_text(strip=True)

                # 跳过空值和"全部"选项
                if value and value != '--全部--' and text and text != '--全部--':
                    # 如果value是数字代码，text包含大学名称
                    if value.isdigit() and len(value) >= 4:
                        university_mapping[text] = value

        # 如果没有找到select，尝试从表格中提取
        if not university_mapping:
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        # 第一列可能是代码，第二列可能是大学名称
                        code = cells[0].get_text(strip=True)
                        name = cells[1].get_text(strip=True)

                        if code.isdigit() and len(code) >= 4 and name:
                            university_mapping[name] = code

        logger.info(f"Found {len(university_mapping)} universities in mapping")
        return university_mapping

    except Exception as e:
        logger.error(f"Error getting university code mapping: {e}")
        return {}

def fetch_university_majors_from_website(university_name, university_code):
    """
    从目标网站获取特定大学的专业信息
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': BASE_URL
        }

        logger.info(f"Fetching majors for {university_name} (code: {university_code})")

        # 首先获取该大学的基本信息
        params = {
            'yxdm': university_code,  # 院校代码
            'pc': '',  # 批次
            'kl': '',  # 科类
            'jhfl': ''  # 计划类别
        }

        # 添加重试机制来处理SSL错误
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(BASE_URL, params=params, headers=headers, timeout=30)
                response.raise_for_status()
                break
            except requests.exceptions.SSLError as e:
                if attempt < max_retries - 1:
                    logger.warning(f"SSL error for {university_name}, retrying... (attempt {attempt + 1})")
                    time.sleep(5)  # 等待5秒后重试
                    continue
                else:
                    raise e

        soup = BeautifulSoup(response.content, 'html.parser')
        majors_data = []

        # 查找包含大学信息的表格（第二个表格）
        tables = soup.find_all('table')
        if len(tables) >= 2:
            university_table = tables[1]  # 第二个表格包含大学信息
            rows = university_table.find_all('tr')

            # 跳过表头，处理数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 9:  # 确保有足够的列
                    row_university_code = cells[0].get_text(strip=True)
                    _ = cells[1].get_text(strip=True)  # row_university_name (not used)
                    batch = cells[2].get_text(strip=True)
                    plan_category = cells[3].get_text(strip=True)
                    subject_category = cells[4].get_text(strip=True)
                    major_count = cells[5].get_text(strip=True)
                    enrollment_count = cells[6].get_text(strip=True)
                    enrollment_scope = cells[7].get_text(strip=True)

                    # 检查是否是目标大学
                    if row_university_code == university_code:
                        # 构建专业信息条目
                        major_info = f"{batch}-{plan_category}-{subject_category}"
                        enrollment_info = f"专业数: {major_count}, 计划数: {enrollment_count}"
                        scope_info = f"招生范围: {enrollment_scope}"

                        majors_data.append([major_info, enrollment_info, scope_info])

        # 如果没有找到专业数据，尝试其他方法或创建占位符
        if not majors_data:
            # 尝试查找其他可能的专业信息
            all_text = soup.get_text()
            if university_name in all_text:
                majors_data = [["找到大学信息", "但无法解析具体专业", f"院校代码: {university_code}"]]
            else:
                majors_data = [["未找到大学信息", f"院校代码: {university_code}", "可能需要手动查询"]]

        logger.info(f"Found {len(majors_data)} major categories for {university_name}")
        return majors_data

    except Exception as e:
        logger.error(f"Error fetching majors for {university_name}: {e}")
        return [["获取失败", f"错误: {str(e)}", f"院校代码: {university_code}"]]

def update_excel_file():
    """更新Excel文件，遍历所有大学并获取专业信息"""

    # 首先获取目标网站的大学代码映射
    university_code_mapping = get_university_code_mapping()
    if not university_code_mapping:
        logger.error("Failed to get university code mapping from target website")
        return

    # 读取大学列表
    try:
        uni_df = pd.read_excel(uni_list_file)
        logger.info(f"Loaded {len(uni_df)} universities from {uni_list_file}")
        logger.info(f"Columns: {uni_df.columns.tolist()}")
    except Exception as e:
        logger.error(f"Error reading {uni_list_file}: {e}")
        return

    # 检查必要的列是否存在
    if 'University Name' not in uni_df.columns:
        logger.error("'University Name' column not found in the Excel file")
        return

    # 检查现有的输出文件
    existing_sheets = []
    if os.path.exists(output_file):
        try:
            with pd.ExcelFile(output_file) as xls:
                existing_sheets = xls.sheet_names
                logger.info(f"Found existing file with {len(existing_sheets)} sheets")
        except Exception as e:
            logger.error(f"Error reading existing file: {e}")

    # 过滤掉无效的大学名称
    valid_universities = uni_df[
        (uni_df['University Name'].notna()) &
        (uni_df['University Name'] != '院校代码') &
        (uni_df['University Name'].str.len() > 1)
    ].copy()

    logger.info(f"Found {len(valid_universities)} valid universities to process")

    # 创建Excel写入器
    try:
        if os.path.exists(output_file):
            with pd.ExcelWriter(output_file, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                process_universities(writer, valid_universities, existing_sheets, university_code_mapping)
        else:
            with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
                process_universities(writer, valid_universities, existing_sheets, university_code_mapping)

    except Exception as e:
        logger.error(f"Error creating Excel writer: {e}")

def process_universities(writer, valid_universities, existing_sheets, university_code_mapping):
    """处理大学列表并写入Excel"""
    processed_count = 0
    skipped_count = 0
    not_found_count = 0

    for _, row in valid_universities.iterrows():
        university_name = row['University Name']

        # 检查是否已经处理过这个大学
        if university_name in existing_sheets:
            logger.info(f"Skipping {university_name} - already exists")
            skipped_count += 1
            continue

        logger.info(f"Processing {university_name} ({processed_count + skipped_count + not_found_count + 1}/{len(valid_universities)})")

        # 查找大学代码
        university_code = university_code_mapping.get(university_name)
        if not university_code:
            # 尝试模糊匹配
            for mapped_name, code in university_code_mapping.items():
                if university_name in mapped_name or mapped_name in university_name:
                    university_code = code
                    logger.info(f"Found fuzzy match: {university_name} -> {mapped_name}")
                    break

        if university_code:
            # 获取专业信息
            majors_data = fetch_university_majors_from_website(university_name, university_code)

            if majors_data:
                # 创建DataFrame
                majors_df = pd.DataFrame(majors_data, columns=["专业名称", "招生信息", "备注"])

                # 写入Excel
                try:
                    majors_df.to_excel(writer, sheet_name=university_name, index=False)
                    logger.info(f"Successfully added {len(majors_data)} majors for {university_name}")
                    processed_count += 1
                except Exception as e:
                    logger.error(f"Error writing data for {university_name}: {e}")
            else:
                # 创建占位符
                placeholder_df = pd.DataFrame([["待收集", "无法获取数据", f"院校代码: {university_code}"]],
                                            columns=["专业名称", "招生信息", "备注"])
                try:
                    placeholder_df.to_excel(writer, sheet_name=university_name, index=False)
                    logger.info(f"Created placeholder for {university_name}")
                    processed_count += 1
                except Exception as e:
                    logger.error(f"Error creating placeholder for {university_name}: {e}")
        else:
            # 没有找到对应的大学代码
            logger.warning(f"University code not found for {university_name}")
            placeholder_df = pd.DataFrame([["未找到院校", "目标网站中未找到该大学", "请检查大学名称"]],
                                        columns=["专业名称", "招生信息", "备注"])
            try:
                placeholder_df.to_excel(writer, sheet_name=university_name, index=False)
                not_found_count += 1
            except Exception as e:
                logger.error(f"Error creating not-found placeholder for {university_name}: {e}")

        # 添加延迟以避免过于频繁的请求
        time.sleep(2)

        # 每处理10个大学后显示进度
        if (processed_count + skipped_count + not_found_count) % 10 == 0:
            logger.info(f"Progress: {processed_count + skipped_count + not_found_count}/{len(valid_universities)} universities processed")

    logger.info(f"Completed! Processed: {processed_count}, Skipped: {skipped_count}, Not found: {not_found_count}")

if __name__ == "__main__":
    update_excel_file()
    print(f"Excel file {output_file} has been updated with university majors data.")
