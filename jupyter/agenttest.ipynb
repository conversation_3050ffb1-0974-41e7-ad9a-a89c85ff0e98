import ipywidgets as widgets
from IPython.display import display

# 启用 notebook widgets
widgets.Widget.widgets

import asyncio, os
from textwrap import dedent

from agno.agent import Agent  
from agno.models.ollama import Ollama
from agno.models.xai import xAI
from agno.models.chutes import Chutes
from agno.models.openrouter.openrouter import OpenRouter
from agno.models.google.gemini import Gemini
from agno.tools.mcp import MCPTools, MultiMCPTools
from agno.tools.reasoning import ReasoningTools
from mem0 import Memory, AsyncMemory

# Create local documentation folder
file_path = os.getcwd()
doc_path = os.path.join(file_path, 'doc')
if not os.path.exists(doc_path):
    os.makedirs(doc_path)

os.environ["CHUTES_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"
os.environ["OPENAI_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"

from textwrap import dedent

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.chutes import Chutes
from agno.models.ollama import Ollama

from agno.tools.mem0 import Mem0Tools
from agno.tools.mcp import MCPTools, MultiMCPTools

# 在笔记本中运行以下代码来启用
import ipywidgets as widgets

USER_ID = "freemank"
SESSION_ID = "agno_session"

# 测试 ipywidgets 是否正常工作
print(f'ipywidgets version: {widgets.__version__}')

# 创建一个简单的测试 widget
test_widget = widgets.IntSlider(
    value=7,
    min=0,
    max=10,
    step=1,
    description='Test:',
)
display(test_widget)

# 创建一个交互式展示
def interactive_function(x, y):
    return x * y

# 创建两个滑块控件
x_slider = widgets.IntSlider(
    value=1,
    min=0,
    max=10,
    step=1,
    description='x:',
    continuous_update=False
)

y_slider = widgets.IntSlider(
    value=1,
    min=0,
    max=10,
    step=1,
    description='y:',
    continuous_update=False
)

# 创建交互式输出
out = widgets.interactive_output(interactive_function, {'x': x_slider, 'y': y_slider})

# 显示控件
display(widgets.VBox([widgets.HBox([x_slider, y_slider]), out]))

# 创建 Agent 配置界面
def create_agent_config_ui():
    global model_type, temperature, max_tokens
    
    model_type = widgets.Dropdown(
        options=['Ollama', 'OpenAI', 'Chutes', 'xAI', 'Gemini'],
        value='Ollama',
        description='Model:',
        style={'description_width': 'initial'}
    )

    temperature = widgets.FloatSlider(
        value=0.7,
        min=0,
        max=2,
        step=0.1,
        description='Temperature:',
        style={'description_width': 'initial'}
    )

    max_tokens = widgets.IntSlider(
        value=1000,
        min=100,
        max=4096,
        step=100,
        description='Max Tokens:',
        style={'description_width': 'initial'}
    )

    def on_change(change):
        if change['type'] == 'change' and change['name'] == 'value':
            print(f'Current configuration:\n'
                  f'Model: {model_type.value}\n'
                  f'Temperature: {temperature.value}\n'
                  f'Max Tokens: {max_tokens.value}')

    model_type.observe(on_change, names='value')
    temperature.observe(on_change, names='value')
    max_tokens.observe(on_change, names='value')

    return widgets.VBox([
        model_type,
        temperature,
        max_tokens
    ])

# 显示配置界面
display(create_agent_config_ui())

print(temperature.value)

config = {  
    "embedder": {  
        "provider": "ollama",  
        "config": {  
            "model": "nomic-embed-text",  
            "ollama_base_url": "http://localhost:11434"  
        }  
    },  
    "llm": {  
        "provider": "openai",  
        "config": { 
            "model": "Qwen/Qwen3-235B-A22B",
            "openai_base_url": "https://llm.chutes.ai/v1",
            "api_key": os.getenv("OPENAI_API_KEY"),
            "temperature": 0.1,
            "max_tokens": 4096
        }  
    },
    # "llm":{
    #     "provider": "ollama",
    #     "config": {
    #         "model": "Qwen3:32b",
    #         "ollama_base_url": "http://localhost:11434",
    #         "temperature": 0.1,
    #         "max_tokens": 4096
    #     }
    # },
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "integration_test",
            "path": "./chroma_db"
        }
    }
}

localmem0 = Mem0Tools(config=config, user_id=USER_ID)

# 创建交互式对话界面
def create_chat_ui():
    # 创建输入框
    text_input = widgets.Textarea(
        value='',
        placeholder='Type your message here...',
        description='Input:',
        layout=widgets.Layout(width='80%', height='100px')
    )

    # 创建输出区域
    output_area = widgets.Output(
        layout=widgets.Layout(width='80%', height='200px', border='1px solid black')
    )

    # 发送按钮
    send_button = widgets.Button(
        description='Send',
        button_style='primary',
        layout=widgets.Layout(width='100px')
    )

    def on_send_click(b):
        with output_area:
            print(f"User: {text_input.value}")
            # 这里可以添加调用 Agent 的逻辑
            text_input.value = ''

    send_button.on_click(on_send_click)

    return widgets.VBox([
        text_input,
        send_button,
        output_area
    ])

# 显示对话界面
display(create_chat_ui())

agent = Agent(
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    tools=[localmem0],
    user_id=USER_ID,
    session_id=SESSION_ID,
    add_state_in_messages=True,
    markdown=True,
    instructions=dedent(
        """
        You have an evolving memory of this user. Proactively capture new personal details,
        preferences, plans, and relevant context the user shares, and naturally bring them up
        in later conversation. Before answering questions about past details, recall from your memory
        to provide precise and personalized responses. Keep your memory concise: store only
        meaningful information that enhances long-term dialogue. If the user asks to start fresh,
        clear all remembered information and proceed anew.
        """
    ),
    show_tool_calls=True,
)

await agent.aprint_response("I live in NYC")
await agent.aprint_response("I lived in San Francisco for 5 years previously")
await agent.aprint_response("I'm going to a Taylor Swift concert tomorrow")

await agent.aprint_response("Summarize all the details of the conversation")

class MemoryEnabledAgent:
    def __init__(self, use_mcp: bool = True):
        try:
            # 初始化基本属性
            self.user_id = "default_user"  # 默认用户ID
            self.agent_id = ""            # 将在 chat 方法中设置
            self.run_id = None           # 可选的运行 ID
            self.model_id = "Qwen/Qwen3-235B-A22B"
            self.api_key = os.getenv("CHUTES_API_KEY")
            self.model = Chutes(id=self.model_id, api_key=self.api_key)
            self._initialized = False
            self.mcp_switch = use_mcp
            self._interaction_count = 0
            
            # 初始化内存配置
            self.memory_config = {  
                "embedder": {  
                    "provider": "ollama",  
                    "config": {  
                        "model": "nomic-embed-text",  
                        "ollama_base_url": "http://localhost:11434"  
                    }  
                },  
                "llm": {  
                    "provider": "openai",  
                    "config": {  
                        "model": self.model_id,  
                        "openai_base_url": "https://llm.chutes.ai/v1",  
                        "api_key": self.api_key,  
                        "temperature": 0.1,
                        "max_tokens": 4096
                    }  
                },
                "vector_store": {
                    "provider": "chroma",
                    "config": {
                        "collection_name": "integration_test",
                        "path": "./chroma_db"
                    }
                }
            }

            # 注意：我们在这里使用 AsyncMemory 而不是 Memory
            self.memory = AsyncMemory.from_config(self.memory_config)
            
            # 初始化 agent
            if self.mcp_switch:
                self.initialize_with_mcp()
            else:
                self.initialize_without_mcp()
                
        except Exception as e:
            print(f"Error initializing MemoryEnabledAgent: {e}")
            raise

    def _get_metadata(self, agent_id: str = ""):
        """生成用于内存操作的元数据"""
        metadata = {
            "user_id": self.user_id,
            "agent_id": agent_id or self.agent_id or "default_agent"
        }
        if self.run_id:
            metadata["run_id"] = self.run_id
        return metadata

    # with MCP
    def initialize_with_mcp(self):
        if not self._initialized:
            self.agent = Agent(
                name="Memory Enabled Agent",
                model=self.model,
                description="""
                You are an AI assistant with persistent memory capabilities.
                You can access and utilize past interactions and knowledge through
                your memory system to provide more contextually aware responses.
                You can use the tools provided to access and manipulate files or other related operations.
                You always provide the final answer in Chinese.=
                """,
                tools=[
                    MultiMCPTools([
                        f"npx -y @modelcontextprotocol/server-filesystem {os.getcwd()}"
                    ]),
                    ReasoningTools(
                        think=True,
                        analyze=True,
                        add_instructions=True
                    )
                ],
                markdown=True,
                show_tool_calls=True,
            )
            self._initialized = True

    # without MCP
    def initialize_without_mcp(self):
        if not self._initialized:
            self.agent = Agent(
                name="Memory Enabled Agent",
                model=self.model,
                user_id=""
                description="""
                You are an AI assistant with persistent memory capabilities.
                You can access and utilize past interactions and knowledge through
                your memory system to provide more contextually aware responses.
                """,
                tools=[
                    ReasoningTools(
                        think=True,
                        analyze=True,
                        add_instructions=True
                    )
                ],
                markdown=True,
                show_tool_calls=True,
            )
            self._initialized = True

    async def chat(self, user_input: str, agent_id: str = ""):
        try:
            if user_input is not None:
                self._interaction_count += 1

            # 获取元数据
            metadata = self._get_metadata(agent_id)

            # 将输入存储到内存
            await self.memory.add(
                messages=[{"role": "user", "content": user_input}],
                metadata=metadata
            )
            
            # 从内存中检索相关上下文
            context = await self.memory.search(
                query=user_input,
                metadata=metadata
            )
            
            # 组合上下文和当前输入
            enhanced_input = f"Context from memory: {context}\nCurrent input: {user_input}"
            
            # 获取 agent 响应
            response = await self.agent.aprint_response(enhanced_input, stream=True)
            
            # 如果 aprint_response 返回 None，尝试使用 run()
            if response is None:
                print("\n⚠️ aprint_response 返回 None，尝试使用 run 方法...")
                response = await self.agent.run(enhanced_input)
            
            # 将响应存储到内存
            await self.memory.add(
                messages=[{"role": "assistant", "content": str(response)}],
                metadata=metadata
            )
            
            return response
            
        except Exception as e:
            print(f"Error in chat: {str(e)}")
            raise
    
    async def forget(self, agent_id: str = ""):
        """删除特定 agent_id 的所有记忆"""
        metadata = self._get_metadata(agent_id)
        await self.memory.delete(metadata=metadata)
