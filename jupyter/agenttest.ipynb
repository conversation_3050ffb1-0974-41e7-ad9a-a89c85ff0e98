{"cells": [{"cell_type": "code", "execution_count": 2, "id": "be0d670e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/q4/d6ykvnns1yqdjjll0stzlxh00000gn/T/ipykernel_1213/3451920760.py:5: DeprecationWarning: Widget.widgets is deprecated.\n", "  widgets.Widget.widgets\n"]}, {"data": {"text/plain": ["{}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import ipywidgets as widgets\n", "from IPython.display import display\n", "\n", "# 启用 notebook widgets\n", "widgets.Widget.widgets"]}, {"cell_type": "markdown", "id": "f4bc3619", "metadata": {}, "source": ["# Agno Agent Test"]}, {"cell_type": "markdown", "id": "490a29ef", "metadata": {}, "source": ["## 1.Import important packages"]}, {"cell_type": "code", "execution_count": 3, "id": "34f88390", "metadata": {}, "outputs": [], "source": ["import asyncio, os\n", "from textwrap import dedent\n", "\n", "from agno.agent import Agent  \n", "from agno.models.ollama import Ollama\n", "from agno.models.xai import xAI\n", "from agno.models.chutes import Chutes\n", "from agno.models.openrouter.openrouter import OpenRouter\n", "from agno.models.google.gemini import Gemini\n", "from agno.tools.mcp import MCPTools, MultiMCPTools\n", "from agno.tools.reasoning import ReasoningTools\n", "from mem0 import Memory, AsyncMemory"]}, {"cell_type": "code", "execution_count": 4, "id": "c4ae7589", "metadata": {}, "outputs": [], "source": ["# Create local documentation folder\n", "file_path = os.getcwd()\n", "doc_path = os.path.join(file_path, 'doc')\n", "if not os.path.exists(doc_path):\n", "    os.makedirs(doc_path)"]}, {"cell_type": "code", "execution_count": 5, "id": "2d1e427f", "metadata": {}, "outputs": [], "source": ["os.environ[\"CHUTES_API_KEY\"] = \"cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7\"\n", "os.environ[\"OPENAI_API_KEY\"] = \"cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7\""]}, {"cell_type": "code", "execution_count": 6, "id": "2e24dd70", "metadata": {}, "outputs": [], "source": ["from textwrap import dedent\n", "\n", "from agno.agent import Agent\n", "from agno.models.openai import OpenAIChat\n", "from agno.models.chutes import Chutes\n", "from agno.models.ollama import Ollama\n", "\n", "from agno.tools.mem0 import Mem0Tools\n", "from agno.tools.mcp import MCPTools, MultiMCPTools\n", "\n", "# 在笔记本中运行以下代码来启用\n", "import ipywidgets as widgets\n", "\n", "USER_ID = \"freemank\"\n", "SESSION_ID = \"agno_session\""]}, {"cell_type": "code", "execution_count": 9, "id": "7e07c1bf", "metadata": {}, "outputs": [], "source": ["config = {  \n", "    \"embedder\": {  \n", "        \"provider\": \"ollama\",  \n", "        \"config\": {  \n", "            \"model\": \"nomic-embed-text\",  \n", "            \"ollama_base_url\": \"http://localhost:11434\"  \n", "        }  \n", "    },  \n", "    \"llm\": {  \n", "        \"provider\": \"openai\",  \n", "        \"config\": { \n", "            \"model\": \"Qwen/Qwen3-235B-A22B\",\n", "            \"openai_base_url\": \"https://llm.chutes.ai/v1\",\n", "            \"api_key\": os.getenv(\"OPENAI_API_KEY\"),\n", "            \"temperature\": 0.1,\n", "            \"max_tokens\": 4096\n", "        }  \n", "    },\n", "    # \"llm\":{\n", "    #     \"provider\": \"ollama\",\n", "    #     \"config\": {\n", "    #         \"model\": \"Qwen3:32b\",\n", "    #         \"ollama_base_url\": \"http://localhost:11434\",\n", "    #         \"temperature\": 0.1,\n", "    #         \"max_tokens\": 4096\n", "    #     }\n", "    # },\n", "    \"vector_store\": {\n", "        \"provider\": \"chroma\",\n", "        \"config\": {\n", "            \"collection_name\": \"integration_test\",\n", "            \"path\": \"./chroma_db\"\n", "        }\n", "    }\n", "}\n", "\n", "localmem0 = Mem0Tools(config=config, user_id=USER_ID)"]}, {"cell_type": "code", "execution_count": null, "id": "3051fc01", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0d45e05de9394dfab2565b7a603a1a04", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">INFO:backoff:Backing off send_request(...) for 0.9s (requests.exceptions.ConnectionError: ('Connection aborted.', \n", "RemoteDisconnected('Remote end closed connection without response')))\n", "</pre>\n"], "text/plain": ["INFO:backoff:Backing off send_request(...) for 0.9s (requests.exceptions.ConnectionError: ('Connection aborted.', \n", "RemoteDisconnected('Remote end closed connection without response')))\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">INFO:backoff:Backing off send_request(...) for 0.8s (requests.exceptions.ConnectionError: ('Connection aborted.', \n", "RemoteDisconnected('Remote end closed connection without response')))\n", "</pre>\n"], "text/plain": ["INFO:backoff:Backing off send_request(...) for 0.8s (requests.exceptions.ConnectionError: ('Connection aborted.', \n", "RemoteDisconnected('Remote end closed connection without response')))\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8914e2ae9af648dab07da555dfcfc6e8", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "df4b51c07b1c41d9ac17564c9e0d82ac", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "aae831c073f64315a0ee4865205ebb70", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}], "source": ["agent = Agent(\n", "    model=Chutes(id=\"Qwen/Qwen3-235B-A22B\", api_key=os.getenv(\"CHUTES_API_KEY\")),\n", "    tools=[localmem0],\n", "    user_id=USER_ID,\n", "    session_id=SESSION_ID,\n", "    add_state_in_messages=True,\n", "    markdown=True,\n", "    instructions=dedent(\n", "        \"\"\"\n", "        You have an evolving memory of this user. Proactively capture new personal details,\n", "        preferences, plans, and relevant context the user shares, and naturally bring them up\n", "        in later conversation. Before answering questions about past details, recall from your memory\n", "        to provide precise and personalized responses. Keep your memory concise: store only\n", "        meaningful information that enhances long-term dialogue. If the user asks to start fresh,\n", "        clear all remembered information and proceed anew.\n", "        \"\"\"\n", "    ),\n", "    show_tool_calls=True,\n", ")\n", "\n", "await agent.aprint_response(\"I live in NYC\", stream=True)\n", "await agent.aprint_response(\"I lived in San Francisco for 5 years previously\")\n", "await agent.aprint_response(\"I'm going to a Taylor Swift concert tomorrow\")\n", "\n", "await agent.aprint_response(\"Summarize all the details of the conversation\")"]}, {"cell_type": "code", "execution_count": null, "id": "922259d5", "metadata": {}, "outputs": [], "source": ["class MemoryEnabledAgent:\n", "    def __init__(self, use_mcp: bool = True):\n", "        try:\n", "            # 初始化基本属性\n", "            self.user_id = \"default_user\"  # 默认用户ID\n", "            self.agent_id = \"\"            # 将在 chat 方法中设置\n", "            self.run_id = None           # 可选的运行 ID\n", "            self.model_id = \"Qwen/Qwen3-235B-A22B\"\n", "            self.api_key = os.getenv(\"CHUTES_API_KEY\")\n", "            self.model = Chutes(id=self.model_id, api_key=self.api_key)\n", "            self._initialized = False\n", "            self.mcp_switch = use_mcp\n", "            self._interaction_count = 0\n", "            \n", "            # 初始化内存配置\n", "            self.memory_config = {  \n", "                \"embedder\": {  \n", "                    \"provider\": \"ollama\",  \n", "                    \"config\": {  \n", "                        \"model\": \"nomic-embed-text\",  \n", "                        \"ollama_base_url\": \"http://localhost:11434\"  \n", "                    }  \n", "                },  \n", "                \"llm\": {  \n", "                    \"provider\": \"openai\",  \n", "                    \"config\": {  \n", "                        \"model\": self.model_id,  \n", "                        \"openai_base_url\": \"https://llm.chutes.ai/v1\",  \n", "                        \"api_key\": self.api_key,  \n", "                        \"temperature\": 0.1,\n", "                        \"max_tokens\": 4096\n", "                    }  \n", "                },\n", "                \"vector_store\": {\n", "                    \"provider\": \"chroma\",\n", "                    \"config\": {\n", "                        \"collection_name\": \"integration_test\",\n", "                        \"path\": \"./chroma_db\"\n", "                    }\n", "                }\n", "            }\n", "\n", "            # 注意：我们在这里使用 AsyncMemory 而不是 Memory\n", "            self.memory = AsyncMemory.from_config(self.memory_config)\n", "            \n", "            # 初始化 agent\n", "            if self.mcp_switch:\n", "                self.initialize_with_mcp()\n", "            else:\n", "                self.initialize_without_mcp()\n", "                \n", "        except Exception as e:\n", "            print(f\"Error initializing MemoryEnabledAgent: {e}\")\n", "            raise\n", "\n", "    def _get_metadata(self, agent_id: str = \"\"):\n", "        \"\"\"生成用于内存操作的元数据\"\"\"\n", "        metadata = {\n", "            \"user_id\": self.user_id,\n", "            \"agent_id\": agent_id or self.agent_id or \"default_agent\"\n", "        }\n", "        if self.run_id:\n", "            metadata[\"run_id\"] = self.run_id\n", "        return metadata\n", "\n", "    # with MCP\n", "    def initialize_with_mcp(self):\n", "        if not self._initialized:\n", "            self.agent = Agent(\n", "                name=\"Memory Enabled Agent\",\n", "                model=self.model,\n", "                description=\"\"\"\n", "                You are an AI assistant with persistent memory capabilities.\n", "                You can access and utilize past interactions and knowledge through\n", "                your memory system to provide more contextually aware responses.\n", "                You can use the tools provided to access and manipulate files or other related operations.\n", "                You always provide the final answer in Chinese.=\n", "                \"\"\",\n", "                tools=[\n", "                    MultiMCPTools([\n", "                        f\"npx -y @modelcontextprotocol/server-filesystem {os.getcwd()}\"\n", "                    ]),\n", "                    ReasoningTools(\n", "                        think=True,\n", "                        analyze=True,\n", "                        add_instructions=True\n", "                    )\n", "                ],\n", "                markdown=True,\n", "                show_tool_calls=True,\n", "            )\n", "            self._initialized = True\n", "\n", "    # without MCP\n", "    def initialize_without_mcp(self):\n", "        if not self._initialized:\n", "            self.agent = Agent(\n", "                name=\"Memory Enabled Agent\",\n", "                model=self.model,\n", "                user_id=\"\"\n", "                description=\"\"\"\n", "                You are an AI assistant with persistent memory capabilities.\n", "                You can access and utilize past interactions and knowledge through\n", "                your memory system to provide more contextually aware responses.\n", "                \"\"\",\n", "                tools=[\n", "                    ReasoningTools(\n", "                        think=True,\n", "                        analyze=True,\n", "                        add_instructions=True\n", "                    )\n", "                ],\n", "                markdown=True,\n", "                show_tool_calls=True,\n", "            )\n", "            self._initialized = True\n", "\n", "    async def chat(self, user_input: str, agent_id: str = \"\"):\n", "        try:\n", "            if user_input is not None:\n", "                self._interaction_count += 1\n", "\n", "            # 获取元数据\n", "            metadata = self._get_metadata(agent_id)\n", "\n", "            # 将输入存储到内存\n", "            await self.memory.add(\n", "                messages=[{\"role\": \"user\", \"content\": user_input}],\n", "                metadata=metadata\n", "            )\n", "            \n", "            # 从内存中检索相关上下文\n", "            context = await self.memory.search(\n", "                query=user_input,\n", "                metadata=metadata\n", "            )\n", "            \n", "            # 组合上下文和当前输入\n", "            enhanced_input = f\"Context from memory: {context}\\nCurrent input: {user_input}\"\n", "            \n", "            # 获取 agent 响应\n", "            response = await self.agent.aprint_response(enhanced_input, stream=True)\n", "            \n", "            # 如果 aprint_response 返回 None，尝试使用 run()\n", "            if response is None:\n", "                print(\"\\n⚠️ aprint_response 返回 None，尝试使用 run 方法...\")\n", "                response = await self.agent.run(enhanced_input)\n", "            \n", "            # 将响应存储到内存\n", "            await self.memory.add(\n", "                messages=[{\"role\": \"assistant\", \"content\": str(response)}],\n", "                metadata=metadata\n", "            )\n", "            \n", "            return response\n", "            \n", "        except Exception as e:\n", "            print(f\"Error in chat: {str(e)}\")\n", "            raise\n", "    \n", "    async def forget(self, agent_id: str = \"\"):\n", "        \"\"\"删除特定 agent_id 的所有记忆\"\"\"\n", "        metadata = self._get_metadata(agent_id)\n", "        await self.memory.delete(metadata=metadata)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}