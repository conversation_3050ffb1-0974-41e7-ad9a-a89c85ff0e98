{"cells": [{"cell_type": "code", "execution_count": null, "id": "b61fe2a1", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import time\n", "\n", "class MixedHandler:\n", "    def __init__(self, name):\n", "        self.name = name\n", "        self.data = None\n", "        self.is_ready = False\n", "        print(f\"同步：{self.name} 已创建实例。\")\n", "        \n", "\n", "    async def async_setup(self):\n", "        \"\"\"\n", "        异步初始化方法。\n", "        \"\"\"\n", "        print(f\"异步：{self.name} 开始异步设置...\")\n", "        await asyncio.sleep(1) # 模拟异步I/O操作，例如连接数据库\n", "        self.is_ready = True\n", "        print(f\"异步：{self.name} 异步设置完成。\")\n", "\n", "    async def fetch_remote_data_async(self, resource_id):\n", "        \"\"\"\n", "        一个异步方法，模拟从远程获取数据。\n", "        \"\"\"\n", "        if not self.is_ready:\n", "            print(f\"警告：{self.name} 尚未准备好。请先调用 async_setup。\")\n", "            return None\n", "\n", "        print(f\"异步：{self.name} 正在为资源 {resource_id} 获取数据...\")\n", "        await asyncio.sleep(0.5) # 模拟网络延迟\n", "        self.data = f\"来自 {self.name} 的 {resource_id} 的异步数据\"\n", "        print(f\"异步：{self.name} 数据获取成功。\")\n", "        return self.data\n", "\n", "    def process_data_sync(self):\n", "        \"\"\"\n", "        一个同步方法，处理已有数据。\n", "        \"\"\"\n", "        if self.data is None:\n", "            return \"同步：没有数据可以处理。\"\n", "\n", "        print(f\"同步：{self.name} 正在处理数据...\")\n", "        time.sleep(0.2) # 模拟CPU密集型工作\n", "        processed = self.data.upper()\n", "        print(f\"同步：{self.name} 数据处理完成。\")\n", "        return processed\n", "\n", "    def get_status_sync(self):\n", "        \"\"\"\n", "        一个简单的同步方法，返回状态。\n", "        \"\"\"\n", "        return f\"同步：{self.name} 状态 - 是否就绪: {self.is_ready}, 当前数据: {'有' if self.data else '无'}\""]}, {"cell_type": "markdown", "id": "aa67cc2c", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": 30, "id": "1c41440e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["同步：A 已创建实例。\n"]}], "source": ["handler_A = MixedHandler(\"A\")\n", "\n", "async def test_async():\n", "    # 这个函数展示了正确的执行顺序\n", "    await handler_A.async_setup()\n", "    print(handler_A.get_status_sync())\n", "    fetched_data = await handler_<PERSON>.fetch_remote_data_async(123)\n", "    print(handler_A.process_data_sync())\n", "\n", "async def test_sync():\n", "    # 先执行 setup\n", "    await handler_A.async_setup()\n", "    print(\"\\n设置完成后的状态:\")\n", "    print(handler_A.get_status_sync())\n", "    \n", "    # 然后获取数据\n", "    data = await handler_<PERSON>.fetch_remote_data_async(123)\n", "    print(\"\\n获取的数据:\")\n", "    print(data)\n", "    \n", "    # 最后处理数据\n", "    print(\"\\n处理后的数据:\")\n", "    print(handler_A.process_data_sync())"]}, {"cell_type": "code", "execution_count": 35, "id": "96ad694a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["异步：A 开始异步设置...\n", "异步：A 异步设置完成。\n", "\n", "设置完成后的状态:\n", "同步：A 状态 - 是否就绪: True, 当前数据: 有\n", "异步：A 正在为资源 123 获取数据...\n", "异步：A 异步设置完成。\n", "\n", "设置完成后的状态:\n", "同步：A 状态 - 是否就绪: True, 当前数据: 有\n", "异步：A 正在为资源 123 获取数据...\n", "异步：A 数据获取成功。\n", "\n", "获取的数据:\n", "来自 A 的 123 的异步数据\n", "\n", "处理后的数据:\n", "同步：A 正在处理数据...\n", "同步：A 数据处理完成。\n", "来自 A 的 123 的异步数据\n", "异步：A 数据获取成功。\n", "\n", "获取的数据:\n", "来自 A 的 123 的异步数据\n", "\n", "处理后的数据:\n", "同步：A 正在处理数据...\n", "同步：A 数据处理完成。\n", "来自 A 的 123 的异步数据\n"]}], "source": ["await test_sync()"]}, {"cell_type": "markdown", "id": "e0642764", "metadata": {}, "source": ["## 协程（Coroutine）和 await 的工作原理\n", "\n", "当你调用一个异步函数（使用 `async def` 定义的函数）时：\n", "\n", "1. **不使用 `await` 时**:\n", "   ```python\n", "   test_sync()  # 返回一个协程对象，但不执行\n", "   ```\n", "   - 这只是创建了一个协程对象\n", "   - 相当于制定了一个「计划」，但没有执行\n", "   - 就像把食谱放在桌上，但没有开始烹饪\n", "\n", "2. **使用 `await` 时**:\n", "   ```python\n", "   await test_sync()  # 实际执行协程\n", "   ```\n", "   - 告诉 Python 的事件循环：「执行这个计划」\n", "   - 事件循环会管理协程的执行\n", "   - 相当于按照食谱开始实际烹饪\n", "\n", "### 工作流程\n", "\n", "1. **创建协程**:\n", "   - 异步函数调用会创建协程对象\n", "   - 这个对象包含了「要做什么」的信息\n", "\n", "2. **调度执行**:\n", "   - `await` 把协程交给事件循环\n", "   - 事件循环负责在适当的时候执行它\n", "\n", "3. **等待完成**:\n", "   - `await` 会等待协程执行完成\n", "   - 在完成之前不会执行后续代码\n", "\n", "这就是为什么没有 `await` 时只能看到协程对象，而加上 `await` 后才能看到实际的执行结果。"]}, {"cell_type": "code", "execution_count": 34, "id": "41abd15d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["不使用 await 的情况:\n", "<coroutine object demo at 0x10af00700>\n", "\n", "使用 await 的情况:\n", "开始执行...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/q4/d6ykvnns1yqdjjll0stzlxh00000gn/T/ipykernel_21595/729039154.py:11: RuntimeWarning: coroutine 'demo' was never awaited\n", "  print(demo())  # 只会打印协程对象\n", "RuntimeWarning: Enable tracemalloc to get the object allocation traceback\n"]}, {"name": "stdout", "output_type": "stream", "text": ["执行完成！\n", "执行结果: demo 的结果\n"]}], "source": ["# 演示协程对象与实际执行的区别\n", "\n", "async def demo():\n", "    print(\"开始执行...\")\n", "    await asyncio.sleep(1)\n", "    print(\"执行完成！\")\n", "    return \"demo 的结果\"\n", "\n", "# 1. 不使用 await - 只获得协程对象\n", "print(\"不使用 await 的情况:\")\n", "print(demo())  # 只会打印协程对象\n", "\n", "print(\"\\n使用 await 的情况:\")\n", "# 2. 使用 await - 实际执行协程\n", "result = await demo()  # 会看到实际的执行过程和结果\n", "print(f\"执行结果: {result}\")"]}, {"cell_type": "markdown", "id": "8d720c5e", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "2513104a", "metadata": {}, "source": ["## ❓问：为什么在Jupyter环境中，不能使用 `asyncio.run()` 来运行异步函数？\n", "\n", "## 🗣️探讨：在Python文件和JupyterNotebook 中执行异步操作的区别\n", "\n", "1. Jupyter Notebook 中（**是已有事件循环的**）:\n", "```python\n", "# 直接使用 await\n", "await async_function()\n", "```\n", "\n", "2. 普通 Python 文件中（**需要创建事件循环**）:\n", "```python\n", "# 需要使用 asyncio.run()\n", "if __name__ == \"__main__\":\n", "    asyncio.run(async_function())\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "bbdd5c52", "metadata": {}, "outputs": [], "source": ["# 在 <PERSON><PERSON>ter 中可以这样运行异步函数\n", "async def example():\n", "    print(\"开始异步操作...\")\n", "    await asyncio.sleep(1)  # 模拟异步操作\n", "    print(\"异步操作完成！\")\n", "    return \"操作结果\"\n", "\n", "# 直接使用 await\n", "result = await example()\n", "print(f\"获得结果: {result}\")"]}, {"cell_type": "markdown", "id": "875cb7b6", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "bff31ff9", "metadata": {}, "source": ["## ❓问：async 和 await 的搭配规则，它们好像不是一对一出现的？\n", "## 🗣️探讨：它们遵循如下使用规则\n", "\n", "1. **基本原则**：\n", "   - `async` 用于定义异步函数\n", "   - `await` 只能在 `async` 函数内部使用\n", "   - `await` 后面必须跟一个可等待对象（协程、Task、Future 等）\n", "\n", "2. **搭配规则**：\n", "   - **如果一个函数内部使用了 `await`，这个函数必须使用 `async` 定义**\n", "   - **如果一个函数使用 `async` 定义，它的调用者必须使用 `await` 等待它的结果**\n", "   - **普通函数内部不能使用 `await`**\n", "\n", "3. **常见错误**：\n", "   - 在普通函数中使用 `await`\n", "   - 不在 `async` 函数中使用 `await`\n", "   - 忘记使用 `await` 等待异步函数的结果"]}, {"cell_type": "code", "execution_count": 36, "id": "ed0e3de5", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "'await' outside async function (4189464266.py, line 12)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[36]\u001b[39m\u001b[32m, line 12\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mdata = await fetch_data()  # 这会导致 SyntaxError\u001b[39m\n           ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m 'await' outside async function\n"]}], "source": ["# 演示 async 和 await 的正确搭配\n", "\n", "# 1. 基本异步函数\n", "async def fetch_data():\n", "    print(\"开始获取数据...\")\n", "    await asyncio.sleep(1)  # 模拟耗时操作\n", "    return \"获取的数据\"\n", "\n", "# 2. 错误示范：普通函数中不能使用 await\n", "def wrong_function():\n", "    try:\n", "        data = await fetch_data()  # 这会导致 SyntaxError\n", "    except SyntaxError as e:\n", "        print(\"错误：普通函数中不能使用 await\")\n", "\n", "# 3. 正确示范：异步函数的组合使用\n", "async def process_data():\n", "    print(\"开始处理...\")\n", "    # 正确：在 async 函数中使用 await\n", "    data = await fetch_data()\n", "    print(f\"处理数据: {data}\")\n", "    return f\"处理后的{data}\"\n", "\n", "# 4. 多个异步操作的组合\n", "async def main_task():\n", "    print(\"\\n=== 开始主任务 ===\")\n", "    # 可以等待多个异步操作\n", "    data = await process_data()\n", "    print(f\"主任务得到结果: {data}\")\n", "    print(\"=== 主任务完成 ===\")\n", "\n", "# 运行示例\n", "await main_task()"]}, {"cell_type": "markdown", "id": "a71226de", "metadata": {}, "source": ["### 我们分析一下异步操作的执行流程\n", "\n", "让我们看看上面的代码中各个函数的依赖关系：\n", "\n", "```\n", "main_task()\n", "    └── await process_data()\n", "         └── await fetch_data()\n", "```\n", "\n", "这里的执行顺序是：\n", "\n", "1. `main_task()` 开始执行\n", "2. 遇到 `await process_data()`，必须等待其完成\n", "3. `process_data()` 中遇到 `await fetch_data()`，必须等待其完成\n", "4. `fetch_data()` 完成后，结果返回给 `process_data()`\n", "5. `process_data()` 处理数据后，结果返回给 `main_task()`\n", "\n", "这种链式调用中，每一步都依赖于上一步的结果，所以必须按顺序等待。如果我们想要实现真正的并行处理，需要使用 `asyncio.gather()` 或 `asyncio.create_task()`。"]}, {"cell_type": "code", "execution_count": 49, "id": "65e3aec1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始执行时间对比测试...\n", "\n", "=== 开始并行任务 ===\n", "开始并行获取数据 1, 2, 3...\n", "得到所有结果: ['数据1', '数据2', '数据3']\n", "并行任务耗时: 2.00秒\n", "=== 并行任务完成 ===\n", "\n", "=== 开始串行任务 ===\n", "开始获取数据 1...\n", "得到所有结果: ['数据1', '数据2', '数据3']\n", "并行任务耗时: 2.00秒\n", "=== 并行任务完成 ===\n", "\n", "=== 开始串行任务 ===\n", "开始获取数据 1...\n", "得到数据 1: 数据1\n", "开始获取数据 2...\n", "得到数据 1: 数据1\n", "开始获取数据 2...\n", "得到数据 2: 数据2\n", "开始获取数据 3...\n", "得到数据 2: 数据2\n", "开始获取数据 3...\n", "得到数据 3: 数据3\n", "串行调用最终结果: ['数据1', '数据2', '数据3']\n", "串行任务耗时: 6.01秒\n", "=== 串行任务完成 ===\n", "\n", "时间对比总结:\n", "并行执行时间: 2.00秒\n", "串行执行时间: 6.01秒\n", "串行/并行时间比率: 3.00倍\n", "得到数据 3: 数据3\n", "串行调用最终结果: ['数据1', '数据2', '数据3']\n", "串行任务耗时: 6.01秒\n", "=== 串行任务完成 ===\n", "\n", "时间对比总结:\n", "并行执行时间: 2.00秒\n", "串行执行时间: 6.01秒\n", "串行/并行时间比率: 3.00倍\n"]}], "source": ["# 演示并行和串行处理多个异步操作的时间差异\n", "\n", "async def fetch_data_with_id(id):\n", "    # 将进度信息作为返回值的一部分，而不是直接打印\n", "    await asyncio.sleep(2)  # 模拟耗时操作\n", "    return f'数据{id}'\n", "\n", "async def concurrent_main():\n", "    print('\\n=== 开始并行任务 ===')\n", "    start_time = time.time()  # 记录开始时间\n", "    \n", "    print('开始并行获取数据 1, 2, 3...')\n", "    # 同时启动多个异步操作\n", "    results = await asyncio.gather(\n", "        fetch_data_with_id(1),\n", "        fetch_data_with_id(2),\n", "        fetch_data_with_id(3)\n", "    )\n", "    \n", "    end_time = time.time()  # 记录结束时间\n", "    elapsed = end_time - start_time  # 计算耗时\n", "    \n", "    print(f'得到所有结果: {results}')\n", "    print(f'并行任务耗时: {elapsed:.2f}秒')\n", "    print('=== 并行任务完成 ===')\n", "    return elapsed  # 返回耗时\n", "\n", "async def sequence_main():\n", "    print('\\n=== 开始串行任务 ===')\n", "    start_time = time.time()  # 记录开始时间\n", "    \n", "    # 顺序执行异步操作\n", "    print('开始获取数据 1...')\n", "    data1 = await fetch_data_with_id(1)\n", "    print(f'得到数据 1: {data1}')\n", "    \n", "    print('开始获取数据 2...')\n", "    data2 = await fetch_data_with_id(2)\n", "    print(f'得到数据 2: {data2}')\n", "    \n", "    print('开始获取数据 3...')\n", "    data3 = await fetch_data_with_id(3)\n", "    print(f'得到数据 3: {data3}')\n", "    \n", "    end_time = time.time()  # 记录结束时间\n", "    elapsed = end_time - start_time  # 计算耗时\n", "    \n", "    results = [data1, data2, data3]\n", "    print(f'串行调用最终结果: {results}')\n", "    print(f'串行任务耗时: {elapsed:.2f}秒')\n", "    print('=== 串行任务完成 ===')\n", "    return elapsed  # 返回耗时\n", "\n", "# 运行对比示例\n", "print('开始执行时间对比测试...')\n", "\n", "parallel_time = await concurrent_main()\n", "series_time = await sequence_main()\n", "\n", "# 计算并显示时间比率\n", "print(f'\\n时间对比总结:')\n", "print(f'并行执行时间: {parallel_time:.2f}秒')\n", "print(f'串行执行时间: {series_time:.2f}秒')\n", "print(f'串行/并行时间比率: {series_time/parallel_time:.2f}倍')\n"]}, {"cell_type": "markdown", "id": "7a465905", "metadata": {}, "source": ["## 异步调用链分析\n", "\n", "让我们分析一下这个异步调用链：\n", "\n", "```python\n", "async def main_task():              # 第1层：主任务\n", "    data = await process_data()     # 需要等待 process_data 完成\n", "\n", "async def process_data():           # 第2层：数据处理\n", "    data = await fetch_data()       # 需要等待 fetch_data 完成\n", "    return f\"处理后的{data}\"\n", "\n", "async def fetch_data():             # 第3层：数据获取\n", "    await asyncio.sleep(1)          # 模拟耗时操作\n", "    return \"获取的数据\"\n", "```\n", "\n", "### 执行顺序和等待点\n", "\n", "1. `main_task()` 开始执行\n", "   - 遇到 `await process_data()`\n", "   - **必须等待** process_data 完成才能继续\n", "\n", "2. `process_data()` 开始执行\n", "   - 遇到 `await fetch_data()`\n", "   - **必须等待** fetch_data 完成才能处理数据\n", "\n", "3. `fetch_data()` 开始执行\n", "   - 遇到 `await asyncio.sleep(1)`\n", "   - 在等待时，可以执行其他异步任务\n", "   - 完成后返回数据\n", "\n", "### 关键点\n", "\n", "1. **必须等待的操作**：\n", "   - `main_task` 必须等待 `process_data` 的结果\n", "   - `process_data` 必须等待 `fetch_data` 的结果\n", "   - 因为每一层都需要用到下一层的返回值\n", "\n", "2. **可以异步处理的部分**：\n", "   - `fetch_data` 中的 `asyncio.sleep(1)` 在等待时\n", "   - 事件循环可以去处理其他异步任务\n", "   - 如果有多个独立的 `fetch_data` 调用，它们可以并发执行"]}, {"cell_type": "code", "execution_count": 50, "id": "19fdcd61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 开始并发任务 ===\n", "开始获取数据 1...\n", "开始获取数据 2...\n", "开始获取数据 3...\n", "所有数据: ['数据1', '数据2', '数据3']\n", "=== 并发任务完成 ===\n", "所有数据: ['数据1', '数据2', '数据3']\n", "=== 并发任务完成 ===\n"]}], "source": ["# 演示并发异步操作\n", "\n", "async def fetch_data_with_id(id: int):\n", "    print(f\"开始获取数据 {id}...\")\n", "    await asyncio.sleep(1)  # 模拟耗时操作\n", "    return f\"数据{id}\"\n", "\n", "async def concurrent_main():\n", "    print(\"\\n=== 开始并发任务 ===\")\n", "    \n", "    # 并发执行多个异步操作\n", "    results = await asyncio.gather(\n", "        fetch_data_with_id(1),\n", "        fetch_data_with_id(2),\n", "        fetch_data_with_id(3)\n", "    )\n", "    \n", "    print(f\"所有数据: {results}\")\n", "    print(\"=== 并发任务完成 ===\")\n", "\n", "# 运行并发示例\n", "await concurrent_main()"]}, {"cell_type": "markdown", "id": "d1e235ed", "metadata": {}, "source": ["## 分析异步嵌套调用\n", "\n", "让我们分析 `main_task` 函数的执行流程：\n", "\n", "```python\n", "async def main_task():                 # 第一层\n", "    data = await process_data()        # 这里必须等待\n", "    print(f\"主任务得到结果: {data}\")\n", "\n", "async def process_data():              # 第二层\n", "    data = await fetch_data()         # 这里必须等待\n", "    return f\"处理后的{data}\"\n", "\n", "async def fetch_data():                # 第三层\n", "    await asyncio.sleep(1)            # 这里可以异步\n", "    return \"获取的数据\"\n", "```\n", "\n", "### 哪些需要等待？\n", "\n", "1. `main_task` 中的 `await process_data()`:\n", "   - **必须等待**，因为需要使用 `process_data` 的返回值来打印结果\n", "\n", "2. `process_data` 中的 `await fetch_data()`:\n", "   - **必须等待**，因为需要先获取数据才能处理\n", "\n", "### 哪些可以异步？\n", "\n", "1. `fetch_data` 中的 `await asyncio.sleep(1)`:\n", "   - **可以异步**，这个等待过程中事件循环可以处理其他任务\n", "   - 如果有多个 `fetch_data` 调用，它们可以并发执行\n", "\n", "### 如何实现真正的并发？\n", "\n", "如果要并发处理多个数据，可以这样改写：\n", "\n", "```python\n", "async def main_task():\n", "    # 使用 asyncio.gather 并发执行多个操作\n", "    results = await asyncio.gather(\n", "        process_data(),\n", "        process_data(),\n", "        process_data()\n", "    )\n", "```\n", "\n", "这样三个 `process_data` 调用会同时进行，而不是按顺序等待。"]}, {"cell_type": "code", "execution_count": null, "id": "6ec02e56", "metadata": {}, "outputs": [], "source": ["# 演示顺序执行与并发执行的区别\n", "\n", "# 1. 顺序执行\n", "async def sequential_task():\n", "    print(\"\\n=== 开始顺序执行 ===\")\n", "    start_time = time.time()\n", "    \n", "    # 顺序执行三个任务\n", "    data1 = await process_data()\n", "    data2 = await process_data()\n", "    data3 = await process_data()\n", "    \n", "    end_time = time.time()\n", "    print(f\"结果: {[data1, data2, data3]}\")\n", "    print(f\"顺序执行耗时: {end_time - start_time:.2f}秒\")\n", "\n", "# 2. 并发执行\n", "async def concurrent_task():\n", "    print(\"\\n=== 开始并发执行 ===\")\n", "    start_time = time.time()\n", "    \n", "    # 并发执行三个任务\n", "    results = await asyncio.gather(\n", "        process_data(),\n", "        process_data(),\n", "        process_data()\n", "    )\n", "    \n", "    end_time = time.time()\n", "    print(f\"结果: {results}\")\n", "    print(f\"并发执行耗时: {end_time - start_time:.2f}秒\")\n", "\n", "# 运行对比\n", "# 先顺序执行\n", "await sequential_task()\n", "# 再并发执行\n", "await concurrent_task()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}