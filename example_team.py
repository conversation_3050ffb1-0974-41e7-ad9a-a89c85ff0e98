from agno.agent.agent import Agent
from agno.models.openai.chat import OpenAIChat
from agno.models.xai import xAI
from agno.models.chutes import Chutes
from agno.team import Team
import os, asyncio

os.environ["XAI_API_KEY"] = "************************************************************************************"
os.environ["CHUTES_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"

def add_element(agent: Agent, element: str) -> str:
    """Add an item to the shopping list and return confirmation. ONLY adds elements that not people.
    
    Args:
        agent (Agent): The agent executing the action
        item (str): The item to add to the shopping list
    
    Returns:
        str: Confirmation message
    """

    if element.lower() not in agent.team_session_state["element_list"]:
        agent.team_session_state["element_list"].append(element.lower())
        return f"Added `{element}` to the element list"
    else:
        return f"The element `{element}` is already in the list"
    
def remove_element(agent: Agent, element: str) -> str:
    """Remove specific item from the element list by name. If more than one elements are going to be removed, recursively call this function for each element. ONLY adds elements that not people

    Args:
        agent (Agent): The agent executing the action
        item (str): The item to remove from the element list

    Returns:
        str: Confirmation message
    """
    # Case-insensitive search
    for i, list_item in enumerate(agent.team_session_state["element_list"]):
        if list_item.lower() == element.lower():
            agent.team_session_state["element_list"].pop(i)
            return f"Removed `{list_item}` from the element list"

    return f"`{element}` was not found in the element list. Current element list: {agent.team_session_state['element_list']}"

async def manage_agent(message: str) -> str:
    agent = Agent(
        name="Element Manager",
        agent_id="element_manager",
        role="Manage the element list",
        model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
        debug_mode=True,
        tools=[add_element, remove_element],
        instructions=[
            "Manage the element list by adding and removing elements",
            "ONLY manage elements, NOT people",
            "Always confirm when elements are added or removed",
            "If the task is done, update the session state to log the changes & chores you've performed"
        ],
        show_tool_calls=True    
    )

    print(f"The Agent {agent.name} is processing message: {message}"),
    await agent.aprint_response(message, stream=True)


def add_people(agent: Agent, people: str) -> str:
    """Add an people to the shopping list and return confirmation.
    
    Args:
        agent (Agent): The agent executing the action
        people (str): The people to add to the shopping list
    
    Returns:
        str: Confirmation message
    """

    if people.lower() not in agent.team_session_state["people_list"]:
        agent.team_session_state["people_list"].append(people.lower())
        return f"Added `{people}` to the people list"
    else:
        return f"The people `{people}` is already in the list"
    
def remove_people(agent: Agent, people: str) -> str:
    """Remove specific people from the people list by name. If more than one elements are going to be removed, recursively call this function for each people.

    Args:
        agent (Agent): The agent executing the action
        people (str): The people to remove from the people list

    Returns:
        str: Confirmation message
    """
    # Case-insensitive search
    for i, list_item in enumerate(agent.team_session_state["people_list"]):
        if list_item.lower() == people.lower():
            agent.team_session_state["people_list"].pop(i)
            return f"Removed `{list_item}` from the people list"

    return f"`{people}` was not found in the people list. Current people list: {agent.team_session_state['people_list']}"

# async def manage_people(message: str):
#     agent = Agent(
#         name="People Manager",
#         agent_id="people_manager",
#         role="Manage the people list",
#         model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
#         debug_mode=False,
#         tools=[add_people, remove_people],
#         instructions=[
#             "Manage the people list by adding and removing people",
#             "ONLY manage people, DO NOT manage any other items",
#             "Always confirm when people are added or removed",
#             "If the task is done, update the session state to log the changes & chores you've performed"
#         ],
        
#         show_tool_calls=True
#     )

    # print(f"The agent {agent.name} is processing message: {message}")
    # await agent.aprint_response(message, stream=True)


people_manager = Agent(
    name="People Manager",
    agent_id="people_manager",
    role="Manage the people list",
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    debug_mode=False,
    tools=[add_people, remove_people],
    instructions=[
        "Manage the people list by adding and removing people",
        "ONLY manage people, DO NOT manage any other items",
        "ONLY use the tools functions that defined in the {tools} list",
        "Always confirm when people are added or removed",
        "If the task is done, update the session state to log the changes & chores you've performed"
    ],
    show_tool_calls=True
)

element_manager = Agent(
    name="Element Manager",
    agent_id="element_manager",
    role="Manage the element list",
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    debug_mode=False,
    tools=[add_element, remove_element],
    instructions=[
        "Manage the element list by adding and removing elements",
        "ONLY manage elements, NOT people",
        "ONLY use the tools functions that defined in the {tools} list",
        "Always confirm when elements are added or removed",
        "If the task is done, update the session state to log the changes & chores you've performed"
    ],
    show_tool_calls=True
)

management_team = Team(
    name="Manager Team",
    team_id="manager_team",
    mode="router",
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    show_tool_calls=True,
    debug_mode=True,
    members=[people_manager, element_manager],
    instructions=[
        "Choose appropriate management agent to process different elements/people.",
        "Forward requests to add/remove elements and people to the Element Manager and People Manager, respectively!"
        "ONLY use the tools that are listed in the agent's tools list.",
    ]
)

#================ Seperator ==================

# Define tools to manage our shopping list
def add_item(agent: Agent, item: str) -> str:
    """Add an item to the shopping list and return confirmation.
    
    Args:
        agent (Agent): The agent executing the action
        item (str): The item to add to the shopping list
    
    Returns:
        str: Confirmation message
    """
    # Add the item if it's not already in the list
    if item.lower() not in [
        i.lower() for i in agent.team_session_state["shopping_list"]
    ]:
        agent.team_session_state["shopping_list"].append(item)
        return f"Added '{item}' to the shopping list"
    else:
        return f"'{item}' is already in the shopping list"


def remove_item(agent: Agent, item: str) -> str:
    """Remove an item from the shopping list by name.

    Args:
        agent (Agent): The agent executing the action
        item (str): The item to remove from the shopping list

    Returns:
        str: Confirmation message
    """
    # Case-insensitive search
    for i, list_item in enumerate(agent.team_session_state["shopping_list"]):
        if list_item.lower() == item.lower():
            agent.team_session_state["shopping_list"].pop(i)
            return f"Removed '{list_item}' from the shopping list"

    return f"'{item}' was not found in the shopping list. Current shopping list: {agent.team_session_state['shopping_list']}"


def remove_all_items(agent: Agent) -> str:
    """Remove all items from the shopping list.
    
    Args:
        agent (Agent): The agent executing the action
    
    Returns:
        str: Confirmation message
    """
    agent.team_session_state["shopping_list"] = []
    return "All items removed from the shopping list"


shopping_list_agent = Agent(
    name="Shopping List Agent",
    role="Manage the shopping list",
    agent_id="shopping_list_manager",
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    debug_mode=True,
    tools=[add_item, remove_item, remove_all_items],
    instructions=[
        "Manage the shopping list by adding and removing items",
        "Always confirm when items are added or removed",
        "If the task is done, update the session state to log the changes & chores you've performed",
    ],
)


# Shopping management team - new layer for handling all shopping list modifications
shopping_mgmt_team = Team(
    name="Shopping Management Team",
    team_id="shopping_management",
    mode="router",
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    show_tool_calls=True,
    debug_mode=True,
    members=[shopping_list_agent],
    instructions=[
        "Manage adding and removing items from the shopping list using the Shopping List Agent",
        "Forward requests to add or remove items to the Shopping List Agent",
    ],
)


def get_ingredients(agent: Agent) -> str:
    """Retrieve ingredients from the shopping list to use for recipe suggestions.
    
    Args:
        agent (Agent): The agent executing the action
        
    Returns:
        str: List of available ingredients or empty list message
    """
    shopping_list = agent.team_session_state["shopping_list"]

    if not shopping_list:
        return "The shopping list is empty. Add some ingredients first to get recipe suggestions."

    # Just return the ingredients - the agent will create recipes
    return f"Available ingredients from shopping list: {', '.join(shopping_list)}"


recipe_agent = Agent(
    name="Recipe Suggester",
    agent_id="recipe_suggester",
    role="Suggest recipes based on available ingredients",
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    tools=[get_ingredients],
    instructions=[
        "First, use the get_ingredients tool to get the current ingredients from the shopping list",
        "After getting the ingredients, create detailed recipe suggestions based on those ingredients",
        "Create at least 3 different recipe ideas using the available ingredients",
        "For each recipe, include: name, ingredients needed (highlighting which ones are from the shopping list), and brief preparation steps",
        "Be creative but practical with recipe suggestions",
        "Consider common pantry items that people usually have available in addition to shopping list items",
        "Consider dietary preferences if mentioned by the user",
        "If no meal type is specified, suggest a variety of options (breakfast, lunch, dinner, snacks)",
    ],
)


def list_items(team: Team) -> str:
    """List all items in the shopping list.
    
    Args:
        team (Team): The team managing the shopping list
        
    Returns:
        str: The formatted shopping list or empty list message
    """
    shopping_list = team.team_session_state["shopping_list"]

    if not shopping_list:
        return "The shopping list is empty."

    items_text = "\n".join([f"- {item}" for item in shopping_list])
    return f"Current shopping list:\n{items_text}"


# Create meal planning subteam
meal_planning_team = Team(
    name="Meal Planning Team",
    team_id="meal_planning",
    mode="coordinate",
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    show_tool_calls=True,
    members=[recipe_agent],
    instructions=[
        "You are a meal planning team that suggests recipes based on shopping list items.",
        "IMPORTANT: When users ask 'What can I make with these ingredients?' or any recipe-related questions, IMMEDIATELY forward the EXACT SAME request to the recipe_agent WITHOUT asking for further information.",
        "DO NOT ask the user for ingredients - the recipe_agent will work with what's already in the shopping list.",
        "Your primary job is to forward recipe requests directly to the recipe_agent without modification.",
    ],
    
)


def add_chore(team: Team, chore: str, priority: str = "medium") -> str:
    """Add a chore to the list with priority level.

    Args:
        chore (str): The chore to add to the list
        priority (str): Priority level of the chore (low, medium, high)

    Returns:
        str: Confirmation message
    """
    # Initialize chores list if it doesn't exist
    if "chores" not in team.session_state:
        team.session_state["chores"] = []

    # Validate priority
    valid_priorities = ["low", "medium", "high"]
    if priority.lower() not in valid_priorities:
        priority = "medium"  # Default to medium if invalid

    # Add the chore with timestamp and priority
    from datetime import datetime

    chore_entry = {
        "description": chore,
        "priority": priority.lower(),
        "added_at": datetime.now().strftime("%Y-%m-%d %H:%M"),
    }

    team.session_state["chores"].append(chore_entry)

    return f"Added chore: '{chore}' with {priority} priority"


shopping_team = Team(
    name="Shopping List Team",
    mode="coordinate",
    model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
    team_session_state={"shopping_list": []},
    tools=[list_items, add_chore],
    session_state={"chores": []},
    team_id="shopping_list_team",
    members=[
        shopping_mgmt_team,
        meal_planning_team,
    ],
    show_tool_calls=True,
    markdown=True,
    instructions=[
        "You are a team that manages a shopping list & helps plan meals using that list.",
        "If you need to add or remove items from the shopping list, forward the full request to the Shopping Management Team.",
        "IMPORTANT: If the user asks about recipes or what they can make with ingredients, IMMEDIATELY forward the EXACT request to the meal_planning_team with NO additional questions.",
        "Example: When user asks 'What can I make with these ingredients?', you should simply forward this exact request to meal_planning_team without asking for more information.",
        "If you need to list the items in the shopping list, use the list_items tool.",
        "If the user got something from the shopping list, it means it can be removed from the shopping list.",
        "After each completed task, use the add_chore tool to log exactly what was done with high priority.",
    ],
    
    show_members_responses=True,
)


#===================== TEST ====================#
if __name__ == "__main__":
    import asyncio
    print("Starting management_team.aprint_response...")
    asyncio.run(management_team.aprint_response("Add milk, eggs, dog, Isaac Newton, Paul Dirac and James Clerk Maxwell to the appropriate list. Then randomly remove ONE people from the list. Finally, show the final items of the two lists in Python list format.", stream=True))
    print("Finished management_team.aprint_response.")

# # Example usage
# shopping_team.print_response(
#     "Add milk, eggs, and bread to the shopping list", stream=True
# )
# print(f"Session state: {shopping_team.team_session_state}")

# shopping_team.print_response("I got bread", stream=True)
# print(f"Session state: {shopping_team.team_session_state}")

# shopping_team.print_response("I need apples and oranges", stream=True)
# print(f"Session state: {shopping_team.team_session_state}")

# shopping_team.print_response("whats on my list?", stream=True)
# print(f"Session state: {shopping_team.team_session_state}")

# # Try the meal planning feature
# shopping_team.print_response("What can I make with these ingredients?", stream=True)
# print(f"Session state: {shopping_team.team_session_state}")

# shopping_team.print_response(
#     "Clear everything from my list and start over with just bananas and yogurt",
#     stream=True,
# )
# print(f"Shared Session state: {shopping_team.team_session_state}")


# print(f"Team session state: {shopping_team.session_state}")