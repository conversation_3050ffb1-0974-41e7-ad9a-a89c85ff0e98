from agno.agent.agent import Agent
from agno.tools.reasoning import ReasoningTools
from agno.models.chutes import Chutes
from agno.models.xai import xAI
from agno.models.ollama import Ollama
from agno.models.openai.chat import OpenAIChat
from agno.team import Team

from agno.tools.mcp import MCPTools, MultiMCPTools
from agno.tools.reasoning import ReasoningTools
from agno.tools.mem0 import Mem0Tools

import os, asyncio, json
from uuid import uuid4

os.environ["OPENAI_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"
os.environ["CHUTES_API_KEY"] = "cpk_476fc0a0bb614808b1a37a53914b140b.1a455af2c05e5439872fb9a6afb30e15.PgUR4oi5Kd8pVqkbsp6P9g0InQuboDe7"
os.environ["XAI_API_KEY"] = "************************************************************************************"

global config

# ========= Agent Memory ========#
config = {  
    "embedder": {  
        "provider": "ollama",  
        "config": {  
            "model": "nomic-embed-text",  
            "ollama_base_url": "http://localhost:11434"  
        }  
    },  
    "llm": {  
        "provider": "openai",  
        "config": { 
            "model": "Qwen/Qwen3-235B-A22B",
            "openai_base_url": "https://llm.chutes.ai/v1",
            "api_key": os.getenv("OPENAI_API_KEY"),
            "temperature": 0.1,
            "max_tokens": 4096
        }  
    },
    # "llm":{
    #     "provider": "ollama",
    #     "config": {
    #         "model": "Qwen3:32b",
    #         "ollama_base_url": "http://localhost:11434",
    #         "temperature": 0.1,
    #         "max_tokens": 4096
    #     }
    # },
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "integration_test",
            "path": "./chroma_db"
        }
    }
}


class AgentCourseDesigner:
    def __init__(self):
        self.model = Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY"))
        self.name = "Course Designer"
        self.agent_id = "course_designer"
        self.session_id = str(uuid4())
        self.role = "Design and manage the course content"
        self.mem_config = {  
             "embedder": {  
                "provider": "ollama",  
                "config": {  
                    "model": "nomic-embed-text",  
                    "ollama_base_url": "http://localhost:11434"  
                }  
            },  
            "llm": {  
                "provider": "openai",  
                "config": { 
                    "model": "Qwen/Qwen3-235B-A22B",
                    "openai_base_url": "https://llm.chutes.ai/v1",
                    "api_key": os.getenv("OPENAI_API_KEY"),
                    "temperature": 0.1,
                    "max_tokens": 4096
                }  
            },
            # "llm":{
            #     "provider": "ollama",
            #     "config": {
            #         "model": "Qwen3:32b",
            #         "ollama_base_url": "http://localhost:11434",
            #         "temperature": 0.1,
            #         "max_tokens": 4096
            #     }
            # },
            "vector_store": {
                "provider": "chroma",
                "config": {
                    "collection_name": "integration_test",
                    "path": "./chroma_db"
                }
            }
        }
        self.agent_memory = Mem0Tools(config=self.mem_config, user_id=self.agent_id)
        
        print("Initializing Course Designer Agent...")
        self.agent = Agent(
            name="Course Designer",
            agent_id="course_designer",
            role="Design and manage the course content",
            # model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("CHUTES_API_KEY")),
            model=xAI(id="grok-3-beta", api_key=os.getenv("XAI_API_KEY")),
            debug_mode=True,
            tools=[ReasoningTools, self.agent_memory, self.list_memory],
            session_state={"course_list": []},  # Initialize session state
            instructions=[
                "You are a professional K-12 STEM course designer, focusing on personalized learning.",  
                "Design the introduction/description of a course and its outline based on the topic and learning objectives provided",  
                "Always ensure the designed content is suitable for the target learners which provided by the user",  
                "Ensure all courses are well-structured and meet educational standards",  
                "Use your memory tools to remember user preferences, past course designs, and important context",  
                "When you learn something important about the user or their requirements, store it in memory for future reference",  
                "If the task is done, update the session state to log the changes & chores you've performed"  
            ],

            show_tool_calls=True
        )

    def analyze_requirements(self, requirements):
        """
        Analyze course requirements from JSON input
        
        Args:
            requirements (dict): JSON containing course requirements
            
        Returns:
            dict: Parsed requirements with standardized keys
        """
        parsed = {
            "topic": requirements.get("topic", ""),
            "learning_objective": requirements.get("learning objective", ""),
            "duration": requirements.get("course duration", ""),
            "target_age": requirements.get("grade/age", "")
        }
        
        # Validate required fields
        if not parsed["topic"] or not parsed["learning_objective"]:
            raise ValueError("Topic and learning objective are required")
            
        return parsed

    def list_memory(self):
        mems_json = self.agent_memory.get_all_memories(self.agent)

        try:
            mems = json.loads(mems_json)

            if not mems:
                print("No memories found.")
                return
            
            print(f"Found {len(mems)} memories.")
            print("*" * 40)

            for i, mem in enumerate(mems):
                print(f"Memory {i+1}:")
                print(f"  User ID: {mem['user_id']}")
                print(f"  ID: {mem['id']}")
                print(f"  Content: {mem['memory']}")
                # print(f"  Timestamp: {mem['user_id']}")
                print("-" * 40)

        except json.JSONDecodeError as e:
            print("Error decoding JSON from memories:", e)
            return



if __name__=="__main__":
    designer = AgentCourseDesigner()
    requirements = {
        "topic": "Introduction to Python Programming",
        "learning objective": "Understand basic Python syntax and data structures",
        "course duration": "4 weeks",
        "grade/age": "High School"
    }
    
    try:
        parsed_requirements = designer.analyze_requirements(requirements)
        print("Parsed Requirements:", parsed_requirements)
        designer.agent.print_response("I wanna to know something about The second law of thermodynamics.")
        designer.list_memory()
    except ValueError as e:
        print("Error:", e)



class TeamManagement:
    def __init__(self):
        self.team = Team(
            name="Management Team",
            team_id="management_team",
            role="Manage the course design and ensure quality",
            model=Chutes(id="Qwen/Qwen3-235B-A22B", api_key=os.getenv("OPENAI_API_KEY")),
            tools=[ReasoningTools, AgentCourseDesigner, MultiMCPTools, Mem0Tools],
            session_state={
                "element_list": [],
                "people_list": []
            },
            instructions=[
                "You are a management team responsible for overseeing the course design process.",
                "Ensure all courses meet educational standards and are suitable for the target learners.",
                "Use your memory tools to remember user preferences, past course designs, and important context.",
                "When you learn something important about the user or their requirements, store it in memory for future reference."
            ],
            show_tool_calls=True,
            debug_mode=True
        )