from fastmcp import FastMCP
  
mcp = FastMCP("test_tools") 
  
@mcp.tool()
def hello_world(name: str) -> str:  
    return f"你好，{name}！这是一个来自 MCP 工具的响应。"
  
@mcp.tool()
def calculate(a: int, b: int, operation: str) -> str:
    if operation == "add":
        return f"{a} + {b} = {a + b}" 
    elif operation == "multiply":
        return f"{a} × {b} = {a * b}"
    else:  
        return f"不支持的操作: {operation}"

if __name__ == "__main__":  
    mcp.run(transport="stdio")