from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import pandas as pd
import time
import os

# Set up the Selenium WebDriver (using Chrome in this case)
options = webdriver.ChromeOptions()
options.add_argument("--headless")  # Run in headless mode
driver = webdriver.Chrome(options=options)

# URL of the page with the list of universities
universities_url = "https://www.eeagd.edu.cn/lzks/yxzycx/yxzycx.html"

# Output Excel file path
output_file = "university_majors.xlsx"

BASE_URL = "https://www.eeagd.edu.cn"

# Function to extract university list and their corresponding "查看专业" elements
def get_university_list():
    driver.get(universities_url)
    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "table")))
    universities = []
    table = driver.find_element(By.TAG_NAME, "table")  # Adjust based on actual page structure
    if table:
        rows = table.find_elements(By.TAG_NAME, "tr")[1:]  # Skip header row if exists
        for row in rows:
            cols = row.find_elements(By.TAG_NAME, "td")
            if len(cols) > 1:
                uni_name = cols[1].text.strip()  # Adjust column index based on actual structure
                view_link_element = None
                for a in row.find_elements(By.TAG_NAME, "a"):
                    if "查看专业" in a.text:
                        view_link_element = a
                        break
                if uni_name and view_link_element:
                    universities.append((uni_name, view_link_element))
    return universities

# Function to extract majors from a university's detail page after clicking the link
def get_majors(uni_name, view_link_element):
    view_link_element.click()
    time.sleep(2)  # Wait for page to load after click
    # Switch to any iframe if present
    try:
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            driver.switch_to.frame(iframes[0])  # Switch to the first iframe
    except Exception as e:
        print(f"Could not switch to iframe for {uni_name}: {str(e)}")
    
    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "table")))
    soup = BeautifulSoup(driver.page_source, 'html.parser')
    table = soup.find('table')  # Adjust based on actual page structure
    majors_data = []
    if table:
        rows = table.find_all('tr')[1:]  # Skip header row if exists
        for row in rows:
            cols = row.find_all('td')
            if len(cols) > 0:
                major_info = [col.text.strip() for col in cols]
                majors_data.append(major_info)
    
    # Switch back to default content
    driver.switch_to.default_content()
    return majors_data

# Main process
def main():
    # Get list of universities
    universities = get_university_list()
    print(f"Found {len(universities)} universities.")

    # Save the list of universities to a file
    uni_list_file = "universities_list.xlsx"
    uni_df = pd.DataFrame(universities, columns=["University Name", "Detail URL"])
    uni_df.to_excel(uni_list_file, index=False)
    print(f"List of universities saved to {uni_list_file}")

    # Note: Due to persistent issues with scraping, the script stops after saving the university list.
    print(f"Script execution stopped after saving the university list due to persistent scraping issues.")

if __name__ == "__main__":
    try:
        main()
    finally:
        driver.quit()
