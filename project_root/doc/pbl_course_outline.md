# 项目式学习课程大纲：Python编程与牛顿第二定律的发现

## 一、课程概述
通过Python编程工具，引导学生以科学家视角重现牛顿第二定律的发现过程，培养计算思维与物理建模能力。

## 二、核心目标
1. 掌握Python在物理实验数据分析中的应用
2. 理解力、质量与加速度的定量关系
3. 培养基于数据驱动的科学发现方法

## 三、课程结构

### 第一周：数据驱动的科学发现
**学习目标**：
- 使用Python分析虚拟实验数据
- 通过数据拟合发现物理规律

**核心活动**：
1. 虚拟斜面实验数据采集（含随机误差）
2. Python数据分析：
   ```python
   import numpy as np
   import matplotlib.pyplot as plt

   # 模拟实验数据（含噪声）
   mass = np.array([0.2, 0.4, 0.6, 0.8, 1.0])  # 质量(kg)
   acceleration = 2.0 * mass / (mass + 0.1) + 0.1*np.random.randn(5)

   # 绘制散点图
   plt.scatter(mass, acceleration)
   plt.xlabel('Mass (kg)')
   plt.ylabel('Acceleration (m/s²)')
   plt.title('Mass vs Acceleration with Noise')
   plt.grid(true)
   plt.show()
   ```
3. 线性回归分析：`np.polyfit()`探索质量-加速度关系

### 第二周：物理建模与计算机仿真
**学习目标**：
- 构建动力学模型
- 验证理论预测

**核心活动**：
1. 建立F=ma的数学模型
2. 编写仿真程序：
   ```python
   def newton_simulation(mass, force, time=5, dt=0.01):
       t = np.arange(0, time, dt)
       acceleration = force/mass
       velocity = acceleration * t
       position = 0.5 * acceleration * t**2
       return t, velocity, position

   # 可视化运动轨迹
   t, v, p = newton_simulation(1.0, 2.0)
   plt.plot(t, p, label='Position')
   plt.plot(t, v, label='Velocity')
   plt.legend()
   plt.xlabel('Time (s)')
   plt.grid(true)
   plt.show()
   ```
3. 对比仿真结果与实验数据

### 第三周：科学探究与创新应用
**学习目标**：
- 探索非理想条件下的修正模型
- 开发物理实验分析工具

**核心活动**：
1. 考虑摩擦力的改进模型：
   $$ a = \frac{F - \mu mg}{m} $$
2. 开发交互式分析工具：
   ```python
   from ipywidgets import interact

   def analyze_experiment(mass, force, friction=0.1):
       acceleration = (force - friction*mass*9.8)/mass
       plt.bar(['Applied', 'Friction'], [force, friction*mass*9.8])
       plt.title(f'Net Force: {force - friction*mass*9.8:.2f} N')
       plt.ylabel('Force (N)')
       plt.show()
       return acceleration

   interact(analyze_experiment, mass=(0.1, 2.0), force=(0, 10), friction=(0, 0.5))
   ```

## 四、评估标准
| 维度 | 权重 | 评估方式 |
|------|------|----------|
| 数据分析能力 | 30% | Jupyter Notebook代码质量 |
| 物理建模能力 | 30% | 模型准确性与创新性 |
| 科学探究能力 | 25% | 实验设计与结论合理性 |
| 技术整合能力 | 15% | Python工具使用熟练度 |

## 五、扩展资源
- Jupyter Notebook物理实验室模板
- Python物理仿真库（VPython）教程
- 科学计算最佳实践指南