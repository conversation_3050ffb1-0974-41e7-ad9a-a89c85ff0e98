# 任务梳理

## 0. 先简单讲一下启动会的内容
demo用的什么框架

## 1. 任务规划
- 前后端分离，前端负责显示React，后端跑业务逻辑Python fastapi flask
- 前端界面简化：单页面
- 后端任务划分：Agent集群结构

## 2. Agent任务划分
划分为教学端和学生端，分别以Team方式来实现
### 2.1 教学端Agent
- 规划Agent: 根据主题确定教学大纲，利用布鲁姆认知框架理论来生成教学大纲，生成课标对应表格（使用推理模型）
- 内容生成Agent: 根据教学大纲，生成具体教学内容，交互性教学内容（文本模型，文生图模型，角色一致性）
- 评价Agent: 工作在任务执行期间，在节点结束时，设计测试，在学生响应之后，形成评价，借助布鲁姆认知框架

### 2.2 学生端Agent
- 提问助手：启发式，不直接给出答案/直接给出答案

### 2.3 必备能力
- 必须支持MCP，这是比赛要求，如不支持，换框架
- 构建记忆机制，采用 `mem0`，记录教师和学生的内容，形成长期记忆


# 任务列表
- 对比几个框架，一天半内完成（MCP， mem0）
- 文生图，一致性，前端页面更新/声音 TTS
- RAG Agent 增量生成，做成后端服务
- 前后端设计 React （两个前端页面：教师端，学生端， 教师端显示课程大纲， 课程内容， 学生端实现互动学习）

---


# 任务概述
采用前后端分离的架构，制作一个网站，前端采用React框架，包含三个页面：
- 着陆页
- 课程规划页面
- 学生端页面

后端对接 Python 服务和其他服务，通过 Rest API 来对接各类服务。

# 页面描述
## 着陆页
- **布局** : 参照`lovable`首页布局，中心位置是一个对话框，等待用户输入。对话框上方是一行标题：「今天你想了解什么？」，在对话框下方是一个漂亮的流动背景色的按钮，按钮上的文本为「去探索」，当用户点击之后，按钮文本变为：「正在生成」。
- **功能** : 当用户点击之后，将用户输入发送给后端 Agent 服务器
## 课程规划页
- **布局** ： 课程规划页面主要用来显示后台Agent所生成的课程内容，采用左右布局，左侧区域为课程大纲，右侧为课程内容概览。
- **功能** ： 
    - 页面字体必须渲染为更漂亮和平滑，且能够支持Markdown和基本的LATEX语法显示，比如公式渲染、Mermaid语法识别和显示、代码区域渲染等
    - 左侧页面负责显示大纲，右侧页面显示左侧某个章节的具体内容，但是要分模块显示，划分为「图片资源」、「主要内容」、「核心知识点」、「课标对齐」等栏目分别显示对应内容
    - 左侧和右侧均要在等待时，设计显示加载动画，即用动画占位符来增加等待趣味，可以用文字模块类的动画效果实现

## 互动学习页
- **功能** ： 互动学习页面主要提供一个对话界面，供用户和后端的Agent进行互动，
- **布局** ： 
    - 左右布局，默认宽度比为1:3，且比例可以调整
    - 左侧显示一些学科内容信息，比如课程纲要，学习进度，成就列表，知识图谱等
    - 右侧是聊天主界面，聊天界面运行逻辑参考微信这类即时通信软件的聊天界面，通过聊天气泡来组织聊天内容，最新的内容在最下方，旧的内容在上方，当内容过多时，聊天气泡会自动滚动到最新的内容，聊天气泡设计必须美观。
    - 右侧页面是单独滚动的，且右侧页面的顶端和底端，各有两个悬浮的面板
    - 右侧顶部悬浮的面板用以显示后端传递的图片，图片是支持动态更新的；
    - 右侧底部的对话面板，用来给用户输入内容，需要提供一个麦克风图标，用来支持用户的语音输入
    - 聊天窗口要有「扬声器」图标，用来控制是否朗读生成的文本内容
