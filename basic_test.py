import asyncio, os
from textwrap import dedent
from agno.agent import Agent  
from agno.models.ollama import Ollama
from agno.models.xai import xAI
from agno.models.openrouter.openrouter import OpenRouter
from agno.models.google.gemini import Gemini
from agno.tools.mcp import MCPTools, MultiMCPTools
from agno.tools.reasoning import ReasoningTools
from agno.memory.v2 import Memory
# from mem0 import Memory



file_path = os.getcwd()
doc_path = os.path.join(file_path, 'doc')
if not os.path.exists(doc_path):
    os.makedirs(doc_path)
    

async def Grok_agent(message: str) -> None:
    os.environ["OPENAI_API_KEY"] = "************************************************************************************"
    
    async with MultiMCPTools([
        # "fastmcp run mcp_server.py",  # 确保 mcp_server.py 在当前目录  
        "fastmcp run mcp_server.py",
        "npx -y @modelcontextprotocol/server-sequential-thinking",
        f"npx -y @modelcontextprotocol/server-filesystem {file_path}"
        ]) as multi_mcp_tools:
            # 创建 memory
            memory = Memory()

            agent = Agent(
                model=xAI(id = "grok-3-beta"),
                tools = [
                     multi_mcp_tools,
                     ReasoningTools(
                        think=True,
                        analyze=True,
                        add_instructions=True
                    )
                ],
                instructions = dedent(
                    """
                    你需要使用 Sequential Thinking 的工具来分解复杂问题，首先思考问题各个方面，然后逐步解决。
                    """
                ),
                show_tool_calls=True,
                memory=memory,
                markdown=True
            )



    print(f"正在处理问题: {message}")
    await agent.aprint_response(message, stream=True)
    # print(memory.get_all())

# model = Gemini(
#      id="gemini-2.5-pro-preview-05-06",
#      api_key="AIzaSyB6Ln0VUP8hn7IFV60aGwC0efPZZqpYcek"
# )

# async def Gemini_agent(message: str) -> None:
#     # os.environ["OPENAI_API_KEY"] = "e166ef72cb3c6d89d55a40008450661c8c339a586cff3bb71e2acabac7a4fcfd"

#     memory = Memory(model=model)

#     async with MultiMCPTools([
#         # "fastmcp run mcp_server.py",  # 确保 mcp_server.py 在当前目录  
#         "fastmcp run mcp_server.py",
#         "npx -y @modelcontextprotocol/server-sequential-thinking",
#         f"npx -y @modelcontextprotocol/server-filesystem {file_path}"
#         ]) as multi_mcp_tools:

#             agent = Agent(
#                 model=model,
#                 tools = [
#                      multi_mcp_tools,
#                      ReasoningTools(
#                         think=True,
#                         analyze=True,
#                         add_instructions=True
#                     )
#                 ],
#                 instructions = dedent(
#                     """
#                     你需要使用 Sequential Thinking 的工具来分解复杂问题，首先思考问题各个方面，然后逐步解决。
#                     """
#                 ),
#                 show_tool_calls=True,
#                 # memory=memory,
#                 markdown=True
#             )



#             print(f"正在处理问题: {message}")
#             await agent.aprint_response(message, stream=True)
#             # print(memory.get_all())




async def Ollama_agent(message: str) -> None:  
    # 初始化 Ollama 模型  
    model = Ollama(
        id="qwen3:32b",  # 使用您本地安装的模型  
        host="http://localhost:11434"  # Ollama 默认地址
    )  

    async with MultiMCPTools([
        # "fastmcp run mcp_server.py",  # 确保 mcp_server.py 在当前目录  
        "npx -y @modelcontextprotocol/server-sequential-thinking",
        f"npx -y @modelcontextprotocol/server-filesystem {file_path}"
    ]) as multi_mcp_tools:
        
        # 创建 memory
        memory = Memory()

        # 创建 agent
        agent = Agent(
            model=model,  
            tools= [
                 multi_mcp_tools,
                 ReasoningTools(
                    think=True,
                    analyze=True
                    # add_instructions=True
                )
            ],
            memory=memory,
            show_tool_calls=True,  
            markdown=True
        )
          
        # 运行 agent 并打印响应  
        print(f"正在处理问题: {message}")  
        await agent.aprint_response(message, stream=True)  
  
if __name__ == "__main__":
    # 测试 agent  
    asyncio.run(Ollama_agent("""
                             ## 生成一个将Python编码训练融入物理学主题学习的项目式学习/问题式学习（PBL）课程大纲，以牛顿第二定律的发现为例；
                             ## 编程作为工具必须完全融入其中，不要单纯的教授Python语法知识，设计具体的物理问题实例来融入Python知识和编程问题；
                             ## 最后将回复内容全部写入项目目录中的`doc`文件夹,如果已存在相似文件，则覆盖它；
                             ## 写入文件的内容必须要和回复内容保持严格一致，包含所有公式、代码等元素。
                             """))